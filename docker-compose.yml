version: '3'
services:
  db:
    image: postgres:16
    restart: always
    volumes:
      - ./postgres-data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DATABASE=${DB_NAME:-homestead}
      - POSTGRES_USER=${DB_USER:-homestead}
      - POSTGRES_PASSWORD=${DB_PASS:-secret}
      - POSTGRES_ROOT_PASSWORD=${DB_PASS:-secret}
      - TZ=${TZ:-Asia/Tokyo}
    ports:
      - ${DB_PORT:-5432}:5432
    networks:
      - clout_network
  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    hostname: rabbitmq
    volumes:
      - rabbit_data:/var/lib/rabbitmq
    ports:
      - '5672:5672'
      - '15672:15672'
    env_file:
      - .env
  redis:
    image: redis:latest
    restart: always
    command: redis-server --save 20 1 --loglevel warning --requirepass master123
    volumes:
      - redis_data:/data
    ports:
      - '6381:6379'
    networks:
      - clout_network
  adminer:
    image: adminer
    restart: always
    ports:
      - 8080:8080
    profiles:
      - debug
    networks:
      - clout_network
volumes:
  rabbit_data:
    driver: local
  redis_data:
    driver: local
networks:
  clout_network:
    name: clout_network
    # external: true
