import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { WHERE_PIPE_FORMAT } from '@clout/common/constants/errors.constants';

@Injectable()
export class WherePipe implements PipeTransform {
  transform(value: string): unknown {
    if (value == null) return undefined;
    try {
      return JSON.parse(value);
    } catch (error) {
      throw new BadRequestException(WHERE_PIPE_FORMAT);
    }
  }
}
