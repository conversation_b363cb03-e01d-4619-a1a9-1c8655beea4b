import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { ORDER_BY_FORMAT } from '@clout/common/constants/errors.constants';

/** Convert a string like "name asc, address desc" to { name: "asc", address: "desc" } */
@Injectable()
export class OrderByPipe implements PipeTransform {
  transform(value: string): unknown {
    if (value == null) return undefined;
    try {
      return JSON.parse(value);
    } catch (_) {
      throw new BadRequestException(ORDER_BY_FORMAT);
    }
  }
}
