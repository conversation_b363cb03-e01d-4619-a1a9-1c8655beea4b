import { Prisma } from '@prisma/client';

export const GET_USER: Prisma.UserSelect = {
  id: true,
  name: true,
  Campaign: true,
  CampaignView: true,
  companyId: true,
  companyRole: true,
  email: true,
  emailId: true,
  identities: {
    where: {
      active: true,
      deletedAt: null,
    },
  },
  isAdmin: true,
  isVerified: true,
  memberCompany: true,
  lastActive: true,
  notificationEmail: true,
  Payment: true,
  pointTotal: true,
  prefersLanguage: true,
  profilePictureUrl: true,
  uuid: true,
  isSuperAdmin: true,
};
