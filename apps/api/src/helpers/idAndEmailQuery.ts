export const idAndEmailQuery = (param) => {
  return param
    ? {
        OR: [
          {
            prefersEmail: {
              email: {
                contains: param,
              },
            },
          },
          !Number.isNaN(parseInt(param))
            ? {
                id: parseInt(param),
              }
            : undefined,
        ],
      }
    : undefined;
};
export const idAndEmailReferralQuery = (param) => {
  return param
    ? {
        OR: [
          {
            prefersEmail: {
              email: {
                contains: param,
              },
            },
          },
          {
            userReferral: {
              some: {
                partner: {
                  prefersEmail: {
                    email: {
                      contains: param,
                    },
                  },
                },
              },
            },
          },
          !Number.isNaN(parseInt(param))
            ? {
                id: parseInt(param),
              }
            : undefined,
          !Number.isNaN(parseInt(param))
            ? {
                userReferral: {
                  some: {
                    partnerId: parseInt(param),
                  },
                },
              }
            : undefined,
        ],
      }
    : undefined;
};
