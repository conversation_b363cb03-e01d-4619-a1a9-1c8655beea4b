/* eslint-disable @typescript-eslint/no-explicit-any */

export function tryTransformJson(e: any): string | object {
  try {
    return JSON.parse(e?.value);
  } catch {
    return e;
  }
}

export function tryTransformBoolean(e: any): boolean {
  try {
    if (e.value == 'true' || e.value == '1' || e.value == true) return true;
    else if (e.value == 'false' || e.value == '0' || e.value == false)
      return false;
    else throw new Error('invalidate');
  } catch (error) {
    return e.value;
  }
}
