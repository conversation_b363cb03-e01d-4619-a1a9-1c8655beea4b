import * as dotenv from 'dotenv'; // see https://github.com/motdotla/dotenv#how-do-i-use-dotenv-with-import
import elasticAPM from 'elastic-apm-node';
dotenv.config();
function configAPM() {
  const ENVIRONMENT = process.env.ENVIRONMENT ?? 'local';
  const APM_SERVER_URL = process.env.APM_SERVER_URL ?? 'http://localhost:8200';
  const APM_SERVER_SECRET_TOKEN =
    process.env.APM_SERVER_SECRET_TOKEN ?? undefined;
  const SERVICE_NAME = process.env.SERVICE_NAME ?? 'example-express';

  const apm = elasticAPM.start({
    serverUrl: APM_SERVER_URL,
    secretToken: APM_SERVER_SECRET_TOKEN,
    serviceName: SERVICE_NAME,
    environment: ENVIRONMENT,
    active: ['staging', 'production'].includes(ENVIRONMENT),
  });
  return apm;
}

const apm = configAPM();

/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */
import {
  utilities as nestWinstonModuleUtilities,
  WinstonModule,
} from 'nest-winston';
import * as os from 'os';
import * as winston from 'winston';
import {
  ElasticsearchTransformer,
  ElasticsearchTransport,
} from 'winston-elasticsearch';

import { Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { json, urlencoded } from 'body-parser';
import { promises } from 'fs';
import helmet from 'helmet';
import { join } from 'path';
import responseTime from 'response-time';
import { AppModule } from './app.module';
import { ExposeInterceptor } from './interceptors/expose.interceptor';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const cloneBuffer = require('clone-buffer');
function createLogger(apm: any) {
  const ELASTICSEARCH_URL =
    process.env.ELASTICSEARCH_URL ?? 'http://localhost:9200';
  const SERVICE_NAME = process.env.SERVICE_NAME ?? 'example-express';
  const esTransportOpts = {
    apm,
    level: 'info',
    dataStream: true,
    transformer: (logData: any) => {
      const transformed: any = ElasticsearchTransformer(logData);
      transformed.service = { name: SERVICE_NAME };
      transformed.host = { name: os.hostname() };
      transformed.container = { id: SERVICE_NAME };
      return transformed;
    },
    clientOpts: {
      node: ELASTICSEARCH_URL,
      ...(process.env.ELASTICSEARCH_USERNAME &&
      process.env.ELASTICSEARCH_PASSWORD
        ? {
            auth: {
              username: process.env.ELASTICSEARCH_USERNAME,
              password: process.env.ELASTICSEARCH_PASSWORD,
            },
          }
        : {}),
    },
  };
  const esTransport = new ElasticsearchTransport(esTransportOpts);
  esTransport.on('error', (error) => {
    console.error('Error in logger caught', error);
  });
  const logger = WinstonModule.createLogger({
    exitOnError: false,
    transports: [
      esTransport,
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.ms(),
          nestWinstonModuleUtilities.format.nestLike(SERVICE_NAME, {})
        ),
      }),
    ],
  });
  return logger;
}

async function bootstrap() {
  const logger = createLogger(apm);
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: logger,
  });
  const configService = app.get(ConfigService);
  const globalPrefix = 'v1';
  app.setGlobalPrefix(globalPrefix);
  app.useGlobalInterceptors(new ExposeInterceptor());
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
    })
  );
  app.use(helmet());
  app.enableCors();
  const pkg = JSON.parse(
    await promises.readFile(join('.', 'package.json'), 'utf8')
  );
  if (process.env.ENABLE_SWAGGER === 'true') {
    const options = new DocumentBuilder()
      .setTitle('API')
      .setDescription('API description')
      .setVersion(pkg.version)
      .addBearerAuth(
        { type: 'http', scheme: 'bearer', bearerFormat: 'JWT', in: 'header' },
        'JWT'
      )
      .addServer('https://api.staging.clout-fi.com/', 'staging')
      .addServer('https://api.test-internal-clout.lisod.vn', 'test')
      .addServer('http://localhost:8888', 'Local')
      .build();
    const document = SwaggerModule.createDocument(app, options);
    SwaggerModule.setup('api', app, document);
  }
  app.use(responseTime());

  app.use(
    json({
      limit: '50mb',
      verify: (req: any, res, buf, encoding) => {
        if (req.headers['stripe-signature'] && Buffer.isBuffer(buf)) {
          req.rawBody = cloneBuffer(buf);
        }
        return true;
      },
    })
  );
  app.use(urlencoded({ extended: true, limit: '50mb' }));
  app.useStaticAssets(join(__dirname, '..', '..', '..', '..', '..', 'static'), {
    prefix: '/static/',
  });
  app.startAllMicroservices();
  const port = process.env.PORT || 3000;

  await app.listen(port);
  Logger.log(
    `🚀 Application is running on: http://localhost:${port}/${globalPrefix}`
  );
}

bootstrap();
