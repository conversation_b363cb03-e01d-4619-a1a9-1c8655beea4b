import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
  logger = new Logger('HttpExceptionFilter');
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;
    if (exception instanceof HttpException) {
      this.logger.error(
        `🚀 Http Exception Error:\n- Call by user: ${
          request?.user?.['id'] ?? 'guest'
        }\n- HTTP Status: ${status} with URL: ${
          request.url
        }.\n- Error Message: ${JSON.stringify(exception.getResponse())}`,
        exception instanceof Error ? exception.stack : ''
        // 'AllExceptionsFilter'
      );
      return response.status(status).json(exception.getResponse());
    }
    response.status(status).json({
      statusCode: status,
      message: 'We got an error in processing this request',
      error: 'Internal server error',
    });
  }
}
