import configuration from '@clout/common/config/configuration';
import { CacheModule } from '@nestjs/cache-manager';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { ScheduleModule } from '@nestjs/schedule';
import { HttpExceptionFilter } from './filters/http-exception.filter';
import { LoggerMiddleware } from './middlewares/logger.middleware';
import { AccountModule } from './modules/accounts/accounts.module';
import { AuthModule } from './modules/auth/auth.module';
import { ScopesGuard } from './modules/auth/scope.guard';
import { StaartAuthGuard } from './modules/auth/staart-auth.guard';
import { CampaignsModule } from './modules/campaigns/campaigns.module';
import { CompaniesModule } from './modules/companies/companies.module';
import { CouponsModule } from './modules/coupons/coupons.module';
import { DefaultModule } from './modules/default/default.module';
import { MaintenancePeriodModule } from './modules/maintenance-periods/maintenance-periods.module';
import { BannersModule } from './modules/banners/banners.module';
import { MeModule } from './modules/me/me.module';
import { PaymentsModule } from './modules/payments/payments.module';
import { RewardsModule } from './modules/rewards/rewards.module';
import { TaskModule } from './modules/tasks/tasks.module';
import { UsersModule } from './modules/users/users.module';
import { RateLimitInterceptor } from './interceptors/rate-limit.interceptor';


@Module({
  imports: [
    CacheModule.register({ isGlobal: true }),
    ConfigModule.forRoot({
      envFilePath: ['.env.api', '.env'],
      load: [configuration],
      isGlobal: true,
    }),
    ScheduleModule.forRoot(),
    DefaultModule,
    AuthModule,
    UsersModule,
    CampaignsModule,
    CompaniesModule,
    MeModule,
    AccountModule,
    TaskModule,
    PaymentsModule,
    CouponsModule,
    RewardsModule,
    MaintenancePeriodModule,
    BannersModule
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: RateLimitInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: StaartAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ScopesGuard,
    },
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }
}
