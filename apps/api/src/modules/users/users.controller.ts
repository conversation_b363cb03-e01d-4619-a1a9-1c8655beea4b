import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { UsersService } from './users.service';
import { AccessTokenParsed } from '../auth/auth.interface';
import { CurrentUser } from '../../helpers/current-user-decorator';
import { ContactDto } from './users.dto';
import { Public } from '../auth/public.decorator';
import { NovuApiKey } from '../../helpers/current-NovuApiKey';

@Controller('users')
@ApiTags('usersAPI')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get(':userId')
  async getDetails(
    @CurrentUser() user: AccessTokenParsed,
    @Param('userId', ParseIntPipe) userId: number
  ) {
    return await this.usersService.getDetails(user.id, +userId);
  }

  @Public()
  @Post('contacts')
  async userContact(
    @Body() contactDto: ContactDto,
    @NovuApiKey() novuApiKey: string
  ) {
    return await this.usersService.userContact(contactDto, novuApiKey);
  }

  @Delete(':userId')
  async delete(@Param('userId', ParseIntPipe) userId: number) {
    return await this.usersService.delete(userId);
  }
}
