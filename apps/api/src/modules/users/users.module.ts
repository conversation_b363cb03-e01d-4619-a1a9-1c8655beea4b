import { Modu<PERSON> } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { AmazonGiftCardModule } from '@clout/common/providers/amazon_gift_card/amazon_gift_card.module';
import { PrismaModule } from '@clout/prisma/prisma.module';
import { NovuModule } from '@clout/common/providers/novu/novu.module';
import { CampaignsModule } from '../campaigns/campaigns.module';

@Module({
  imports: [AmazonGiftCardModule, PrismaModule, NovuModule, CampaignsModule],
  providers: [UsersService],
  controllers: [UsersController],
})
export class UsersModule {}
