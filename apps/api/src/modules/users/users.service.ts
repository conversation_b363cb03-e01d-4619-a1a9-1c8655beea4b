import { PrismaService } from '@clout/prisma/prisma.service';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  UNAUTHORIZED_RESOURCE,
  USER_NOT_FOUND,
} from '@clout/common/constants/errors.constants';
import { ContactDto } from './users.dto';
import { NovuService } from '@clout/common/providers/novu/novu.service';
import { USER_CONTACT } from '@clout/common/providers/novu/novu.constant';
import { randomUUID } from 'crypto';
import { GET_USER } from '../../shares/prisma/user.select';
import { Novu } from '@novu/node';
import { CampaignsService } from '../campaigns/campaigns.service';

@Injectable()
export class UsersService {
  logger = new Logger(UsersService.name);
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private campaignsService: CampaignsService,
    private novuService: NovuService
  ) {}

  async getDetails(currentId: number, userId: number) {
    const current = await this.prisma.user.findUnique({
      where: { id: currentId },
    });
    if (!current) throw new ForbiddenException(UNAUTHORIZED_RESOURCE);
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: GET_USER,
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    return user;
  }

  async userContact(contactDto: ContactDto, apiKey: string) {
    try {
      const novuSubsNew = randomUUID();
      const novu = new Novu(apiKey);

      await novu.subscribers.identify(novuSubsNew, {
        email: contactDto.email,
      });
      const {
        firstName,
        lastName,
        email,
        phoneNumber,
        companyName,
        purposeOfInquiry,
        contentOfInquiry,
      } = contactDto;

      await novu.trigger(USER_CONTACT, {
        to: {
          email: email,
          subscriberId: novuSubsNew,
        },
        payload: {
          title: '【clout】お問合せありがとうございました。',
          content: `<p>${firstName} ${lastName}</p><p>お問合せありがとうございます。</p><p>下記内容にてお問合せを承りました。</p><p>お名前: ${firstName} ${lastName}</p><p>Email: ${email}</p>${
            phoneNumber ? `<p>電話番号: ${phoneNumber}</p>` : ''
          }${
            companyName ? `<p>組織名: ${companyName}</p>` : ''
          }<p>お問い合わせ種別: ${purposeOfInquiry}</p><p>お問い合わせ内容: ${contentOfInquiry}</p><p>内容確認の上、担当者よりご連絡させて頂きます。</p><p>回答まで、少々お時間を頂ければと存じます。</p><p>どうぞよろしくお願い致します。</p>`,
        },
        overrides: {
          email: {
            integrationIdentifier:
              process.env.PROVIDER_IDENTIFIER_NOVU_EMAIL_SUPPORT || '',
          },
        },
      });

      await novu.trigger(USER_CONTACT, {
        to: {
          email: '<EMAIL>',
          subscriberId: 'acf8ebd4-b272-4aaa-8ccc-a7c8b13ceb2d',
        },
        payload: {
          title: '新たに「問い合せ」を受信しました！',
          content: `<p>お名前: ${firstName} ${lastName}</p><p>Email: ${email}</p>${
            phoneNumber ? `<p>電話番号: ${phoneNumber}</p>` : ''
          }${
            companyName ? `<p>組織名: ${companyName}</p>` : ''
          }<p>お問い合わせ種別: ${purposeOfInquiry}</p><p>お問い合わせ内容: ${contentOfInquiry}</p>`,
        },
        overrides: {
          email: {
            integrationIdentifier:
              process.env.PROVIDER_IDENTIFIER_NOVU_EMAIL_SUPPORT || '',
          },
        },
      });

      return { status: true, message: 'Success' };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async delete(userId: number) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: GET_USER,
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    try {
      if (user.Campaign.length) {
        for (const campaign of user.Campaign) {
          await this.campaignsService.delete(campaign.id, true);
        }
      }
      await this.prisma.userCampaign.deleteMany({ where: { userId: user.id } });
      await this.prisma.identity.deleteMany({ where: { userId: user.id } });
      await this.prisma.companyRole.deleteMany({ where: { userId } });
      await this.prisma.session.deleteMany({ where: { userId } });
      if (user.emailId)
        await this.prisma.email.delete({ where: { id: user.emailId } });
      await this.prisma.user.delete({ where: { id: userId } });
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      console.log({ error });
    }
    return user;
  }
}
