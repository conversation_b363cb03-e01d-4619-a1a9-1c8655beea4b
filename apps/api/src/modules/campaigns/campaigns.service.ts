import { PrismaService } from '@clout/prisma/prisma.service';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotAcceptableException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  CreateCampaignDraftDto,
  GetCampaignListParamsDto,
  GetDetailsCampaignDto,
  GetIsWinParamsDto,
  GetUserCompletedCampaignListParamsDto,
  ShareUrlDto,
  UpdateCampaignDto,
  WinnerManuallyDto,
} from './campaigns.dto';
import { Configuration } from '@clout/common/config/configuration.interface';
import { S3Service } from '@clout/common/providers/s3/s3.service';
import {
  CAMPAIGN_NOT_FOUND,
  CAN_NOT_DELETE_CAMPAIGN,
  COMPANY_NOT_FOUND,
  IMAGE_TOO_LARGE,
  INVALID_CREDENTIALS,
  NOT_HAVE_PERMISSION,
  USER_NOT_FOUND,
} from '@clout/common/constants/errors.constants';
import {
  CampaignEndReason,
  CampaignStatus,
  IdentityType,
  Membership,
  MethodOfselectWinners,
  PaymentType,
  Prisma,
  TaskType,
} from '@prisma/client';
import { Cron } from '@nestjs/schedule';
import { Response } from 'express';
import { stringify } from 'csv-stringify';
import moment from 'moment-timezone';
import dayjs from 'dayjs';
import { GET_USER } from '../../shares/prisma/user.select';
import { PaymentsService } from '../payments/payments.service';
import { TwitterApi } from 'twitter-api-v2';

interface canParticipateInDraw {
  userCampaignId: number;
  userId: number;
  email: string;
  identityAccountName: string;
  points: number;
}

@Injectable()
export class CampaignsService {
  logger = new Logger(CampaignsService.name);
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private s3Service: S3Service,
    private paymentsService: PaymentsService
  ) { }

  async create(userId: number, createCampaignDraftDto: CreateCampaignDraftDto) {
    const {
      campaignImage,
      setExpiredTime,
      settingForNotWin,
      isWaitingPurcare,
      ...createData
    } = createCampaignDraftDto;
    this.logger.log(
      `{
        userId: ${userId},
        setExpiredTime: ${setExpiredTime},
        settingForNotWin: ${settingForNotWin},
        isWaitingPurcare: ${isWaitingPurcare},
        createData: ${JSON.stringify(createData)}
      }`
    );

    if (campaignImage.size >= 2 * 1024 * 1024) {
      throw new BadRequestException(IMAGE_TOO_LARGE)
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { companyId: true, email: true, companyRole: true },
    });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    if (!user.companyId || !user?.companyRole?.isVerified)
      throw new NotFoundException(COMPANY_NOT_FOUND);

    try {
      const imageUrl = campaignImage
        ? await this.transformMulterImageData(userId, campaignImage)
        : null;
      const newCampaign = await this.prisma.campaign.create({
        data: {
          ...createData,
          setExpiredTime: setExpiredTime == 'true',
          settingForNotWin: settingForNotWin == 'true',
          isWaitingPurcare: isWaitingPurcare == 'true',
          createdUser: {
            connect: {
              id: userId,
            },
          },
          company: {
            connect: {
              id: user.companyId,
            },
          },
          ...(imageUrl
            ? {
              image: {
                create: {
                  imageUrl,
                },
              },
            }
            : {}),
        },
        include: {
          image: true,
          CampaignReward: true,
          createdUser: true,
        },
      });
      return { newCampaign };
    } catch (error) {
      throw new BadRequestException(error?.message);
    }
  }

  async list(query: GetCampaignListParamsDto, userId?: number) {
    let campaigns = [];
    let total = 0;
    const { skip, take, orderBy, status, actionFrom, q } = query;
    console.log({ userId, orderBy, status, actionFrom });
    const filterData: Prisma.CampaignWhereInput = {
      company: {
        OR: [
          {
            name: {
              contains: q,
              mode: 'insensitive',
            },
          },
          {
            email: {
              email: {
                contains: q,
                mode: 'insensitive',
              },
            },
          },
          {
            code: {
              contains: q,
              mode: 'insensitive',
            },
          },
          {
            id: {
              equals: +q || 0,
            },
          },
        ],
      },
    };

    if (actionFrom == 'ADMIN') {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        select: { companyId: true, companyRole: true, isSuperAdmin: true },
      });

      if (!user) throw new NotFoundException(USER_NOT_FOUND);

      if (!user.isSuperAdmin) {
        if (!user.companyId || !user?.companyRole?.isVerified)
          throw new NotFoundException(COMPANY_NOT_FOUND);
        filterData.companyId = user?.companyId;
      }
      if (status) filterData.status = status;
    } else {
      filterData.status = CampaignStatus.PUBLIC;
    }

    try {
      if (query.except) {
        filterData.id = {
          not: query.except,
        };
      }
      [campaigns, total] = await this.prisma.$transaction([
        this.prisma.campaign.findMany({
          ...(skip ? { skip } : {}),
          ...(take ? { take } : {}),
          where: filterData,
          include: {
            image: true,
            CampaignReward: {
              orderBy: {
                id: 'asc',
              },
            },
            company: {
              select: {
                name: true,
                code: true,
                // image: true,
              },
            },
          },
          ...(orderBy
            ? { orderBy }
            : {
              orderBy: {
                createdAt: 'desc',
              },
            }),
        }),
        this.prisma.campaign.count({ where: filterData }),
      ]);
      return { campaigns, total };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async details(
    campaignId: string,
    query: GetDetailsCampaignDto,
    userId?: number
  ) {
    const { currentId, shareToken, isAdmin } = query;
    console.log({ campaignId, userId, currentId, shareToken, isAdmin });
    let isSuperAdmin = false;
    const filterData: Prisma.CampaignWhereInput = {};
    let user = null;

    if (userId) {
      user = await this.prisma.user.findUnique({
        where: { id: userId },
      });
      if (!user) throw new NotFoundException(USER_NOT_FOUND);
      if (user.isSuperAdmin) isSuperAdmin = true;
    }

    const campaign = await this.prisma.campaign.findFirst({
      where: {
        id: campaignId,
        ...filterData,
      },
      include: {
        ...(userId
          ? {
            UserClaimCampaign: {
              where: {
                userId,
              },
              include: {
                award: {
                  include: {
                    campaignReward: true,
                  },
                },
              },
            },
          }
          : {}),
        image: true,
        company: {
          select: {
            name: true,
            code: true,
            image: true,
          },
        },
        createdUser: {
          select: {
            companyId: true,
            companyRole: true,
            email: true,
            emailId: true,
            identities: true,
            isAdmin: true,
            name: true,
          },
        },
      },
    });

    if (
      isAdmin &&
      campaign?.companyId != user?.companyId &&
      !user?.isSuperAdmin
    )
      throw new UnauthorizedException(INVALID_CREDENTIALS);

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);
    await this.prisma.campaign.update({
      where: { id: campaign.id },
      data: { totalViews: campaign.totalViews + 1 },
    });

    if (isAdmin || isSuperAdmin) {
      const playerWin = await this.prisma.userCampaign.findMany({
        where: {
          campaignId: campaign?.id,
        },
        include: { award: true },
        orderBy: {
          award: {
            value: query?.sort === 'asc' ? 'asc' : 'desc'
          }
        },
        distinct: ['userId']
      });

      campaign['playerWin'] = playerWin;
    }

    const checkWinner = await this.prisma.userAward.findFirst({
      where: {
        userCampaign: {
          campaignId
        }
      }
    })

    if(checkWinner) campaign['isWon'] = true;

    const payment = await this.prisma.payment.findMany({
      where: {
        campaignId: campaign?.id,
      },
    });

    const userAwardId = campaign?.UserClaimCampaign?.find(
      (item) => item?.award?.id
    )?.award?.id;
    if (userAwardId) {
      const coupon = await this.prisma.coupon.findFirst({
        where: {
          userAwardId,
        },
      });

      campaign['coupon'] = coupon;
    } else {
      campaign['coupon'] = null;
    }

    campaign['amount'] =
      payment
        ?.filter((item) => item.type === 'WITHDRAWAL')
        ?.map((item) => item.amount ?? 0)
        ?.reduce((acc, curr) => acc + +(curr ?? 0), 0) ?? 0;
    campaign['pointUse'] =
      payment
        ?.filter((item) => item.type === 'PAYMENT')
        ?.map((item) => item.pointUse ?? 0)
        ?.reduce((acc, curr) => acc + +(curr ?? 0), 0) ?? 0;

    return campaign;
  }

  async update(
    userId: number,
    campaignId: string,
    updateCampaignDto: UpdateCampaignDto
  ) {
    const {
      campaignImage,
      setExpiredTime,
      settingForNotWin,
      isWaitingPurcare,
      ...updateData
    } = updateCampaignDto;
    this.logger.log(
      `{
        userId: ${userId},
        campaignId: ${campaignId},
        updateData: ${JSON.stringify(updateData)},
        setExpiredTime: ${setExpiredTime},
        settingForNotWin: ${settingForNotWin},
        isWaitingPurcare: ${isWaitingPurcare}
      }`
    );

    if (campaignImage?.size >= 2 * 1024 * 1024) {
      throw new BadRequestException(IMAGE_TOO_LARGE)
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: GET_USER,
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });
    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);
    if (campaign.companyId !== user.companyId && !user.isSuperAdmin)
      throw new UnauthorizedException(INVALID_CREDENTIALS);
    if (
      updateCampaignDto?.status == CampaignStatus.UNDER_REVIEW &&
      user.companyRole?.membership != Membership.MANAGER &&
      !user.isSuperAdmin
    )
      throw new UnauthorizedException(INVALID_CREDENTIALS);
    if (
      campaign.status == CampaignStatus.PUBLIC &&
      updateCampaignDto.status != CampaignStatus.COMPLETION &&
      updateCampaignDto.status != CampaignStatus.PUBLIC
    )
      throw new UnauthorizedException(INVALID_CREDENTIALS);

    try {
      const imageUrl = campaignImage
        ? await this.transformMulterImageData(userId, campaignImage)
        : null;
      if (imageUrl && campaign?.imageId) {
        await this.prisma.image.delete({ where: { id: campaign.imageId } });
      }
      const newCampaign = await this.prisma.campaign.update({
        where: { id: campaign.id },
        data: {
          ...updateData,
          ...(isWaitingPurcare
            ? { isWaitingPurcare: isWaitingPurcare == 'true' }
            : {}),
          ...(setExpiredTime
            ? setExpiredTime == 'true'
              ? {
                setExpiredTime: true,
              }
              : { setExpiredTime: false, expiredTime: null }
            : {}),
          ...(settingForNotWin
            ? {
              settingForNotWin: settingForNotWin == 'true',
            }
            : {}),
          ...(imageUrl
            ? {
              image: {
                create: {
                  imageUrl,
                },
              },
            }
            : {}),
          ...(updateData.status == CampaignStatus.COMPLETION
            ? {
              setExpiredTime: true,
              expiredTime: new Date(),
              endReason: CampaignEndReason.OTHER,
            }
            : {}),
          ...(updateData.methodOfselectWinners ==
            MethodOfselectWinners.MANUAL_SELECTION
            ? {
              totalPrizeValue: null,
            }
            : {}),
          emailUserUpdated: user?.email?.email,
          idUserUpdated: user?.id,
          timeUpdated: new Date(),
          updatedAt: new Date(),
        },
        include: {
          image: true,
          CampaignReward: {
            include: { userAward: true },
          },
          createdUser: {
            select: GET_USER,
          },
        },
      });
      if (
        updateData.status == CampaignStatus.COMPLETION &&
        newCampaign.methodOfselectWinners ==
        MethodOfselectWinners.AUTO_PRIZEE_DRAW
      ) {
        await this.paymentsService.calculatePayment(
          !user.isSuperAdmin ? user.id : newCampaign.createdUser.id,
          newCampaign.id
        );
      }

      return { newCampaign };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async delete(campaignId: string, isTest?: boolean) {
    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
      include: {
        Task: {
          include: { taskTemplate: true },
        },
      },
    });

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);

    if (campaign.status != CampaignStatus.DRAFT && !isTest)
      throw new ForbiddenException(CAN_NOT_DELETE_CAMPAIGN);

    if (campaign?.imageId)
      await this.prisma.image.delete({ where: { id: campaign?.imageId } });

    await this.prisma.campaignReward.deleteMany({
      where: { campaignId: campaign.id },
    });

    await this.prisma.userCampaign.deleteMany({
      where: { campaignId: campaign.id },
    });

    await this.prisma.campaignView.deleteMany({
      where: { campaignId: campaign.id },
    });

    if (campaign.Task?.length > 0) {
      const taskTemplateIds = [];
      const taskIds = [];
      for (const task of campaign.Task) {
        taskIds.push(task?.id);
        taskTemplateIds.push(task?.taskTemplateId);
      }
      await this.prisma.userTask.deleteMany({
        where: { taskId: { in: taskIds } },
      });
      await this.prisma.task.deleteMany({ where: { id: { in: taskIds } } });
      await this.prisma.taskTemplate.deleteMany({
        where: { id: { in: taskTemplateIds } },
      });
    }
    await this.prisma.campaign.delete({ where: { id: campaign.id } });

    return campaign;
  }

  async getUserCompletedCampaign(
    res: Response,
    userId: number,
    campaignId: string,
    query: GetUserCompletedCampaignListParamsDto
  ) {

    const { skip = 0, take = 10, field = 'createAt', sort = 'desc' } = query

    const orderBy = {};

    if (field === 'award') {
      orderBy['award'] = {
        value: sort === 'asc' ? 'asc' : 'desc',
      };
    } else if (field === 'createdAt') {
      orderBy['createdAt'] = sort === 'asc' ? 'asc' : 'desc';
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);
    if (
      (!user?.companyId || user?.companyId !== campaign.companyId) &&
      !user.isSuperAdmin
    )
      throw new NotAcceptableException(NOT_HAVE_PERMISSION);

    const userCampaigns = await this.prisma.userCampaign.findMany({
      where: {
        campaignId,
      },
      include: {
        user: {
          select: {
            identities: true,
            name: true,
            email: true,
            UserTask: {
              where: { task: { type: TaskType.CUSTOM, campaignId } },
              orderBy: {
                taskId: 'asc',
              },
            },
          },
        },
        award: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    let users = userCampaigns;

    const withAward = users?.filter(item => item?.award !== null);
    const withoutAward = users?.filter(item => item?.award === null);

    if (field === 'award') {
      // sắp xếp theo value cả award
      withAward.sort((a, b) => {
        if (sort === 'asc') {
          return (a.award.value || 0) - (b.award.value || 0);
        } else {
          return (b.award.value || 0) - (a.award.value || 0);
        }
      });
    }

    if (sort === 'asc' && field === 'award') {
      users = withoutAward.concat(withAward);
    } else {
      users = withAward.concat(withoutAward);
    }

    users = userCampaigns.slice(skip, skip + take);
    const amountAwarded = userCampaigns.reduce((total, user) => {
      return total + Number(user?.award?.value ?? 0);
    }, 0);

    if (users.length > 0 && campaign.methodOfselectWinners === 'MANUAL_SELECTION') {
      for (const user of users) {
        const userTasks = await this.prisma.userTask.findMany({
          where: {
            userId: user.userId,
            task: {
              campaignId,
            },
          },
          include: {
            task: {
              include: {
                taskTemplate: true,
              },
            },
          },
        });

        const points = userTasks
          ?.map((item) => item.task?.taskTemplate?.points ?? 0)
          .reduce((acc, curr) => acc + +(curr ?? 0), 0);
        user['points'] = points ?? 0;
      }
    }

    res.send({
      users,
      total: userCampaigns.length,
      amountAwarded,
    });
    res.end();
  }

  async getUserCompleteCampaignChart(
    res: Response,
    userId: number,
    campaignId: string,
    query: GetUserCompletedCampaignListParamsDto
  ) {
    const { dateType } = query;
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);

    if (
      (!user?.companyId || user?.companyId !== campaign.companyId) &&
      !user.isSuperAdmin
    )
      throw new NotAcceptableException(NOT_HAVE_PERMISSION);

    const listTaskRequired = await this.prisma.task.findMany({
      where: {
        taskTemplate: {
          required: true,
        },
        campaignId,
      },
    });

    if (!listTaskRequired.length) {
      throw new BadRequestException(
        'This campaign does not have any required task'
      );
    }

    const startAt = dayjs(campaign.startTime).startOf('day');
    let endAt = dayjs(campaign.expiredTime);
    const today = dayjs().endOf('day');

    if (!endAt.isValid() || endAt.isBefore(today) || endAt.isSame(today)) {
      endAt = today;
    }

    const dateRange = this.getDateRange(
      startAt.toDate(),
      endAt.toDate(),
      dateType
    );

    const data = {};
    let dateBefore = dateRange[0];
    for (const date of dateRange) {
      const currentDate = dayjs(date).toDate();
      const newUserCompleteCampaign = await this.prisma.userCampaign.count({
        where: {
          campaignId,
          createdAt: {
            gt: dayjs(dateBefore).endOf('day').toDate(),
            lte: dayjs(currentDate).endOf('day').toDate(),
          },
        },
      });
      dateBefore = currentDate;

      const totalUserCompleteCampaign = await this.prisma.userCampaign.count({
        where: {
          campaignId,
          createdAt: {
            gte: startAt.toDate(),
            lte: dayjs(currentDate).endOf('day').toDate(),
          },
        },
      });

      data[date] = {
        newUserCompleteCampaign,
        totalUserCompleteCampaign,
      };
    }

    res.send({
      data,
    });
    res.end();
  }

  async getStatisticsCampaign(
    res: Response,
    userId: number,
    campaignId: string,
    query: GetUserCompletedCampaignListParamsDto
  ) {
    const { skip = 0, take = 10 } = query;
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);

    if (
      (!user?.companyId || user?.companyId !== campaign.companyId) &&
      !user.isSuperAdmin
    )
      throw new NotAcceptableException(NOT_HAVE_PERMISSION);

    const campaignViews = await this.prisma.campaignView.findMany({
      include: {
        userShare: {
          select: {
            identities: {
              where: {
                active: true,
                type: IdentityType.TWITTER,
                deletedAt: null,
              },
            },
            id: true,
            name: true,
            email: true,
          },
        },
      },
      where: {
        campaignId,
        userShareId: {
          not: null,
        },
      },
      distinct: ['userShareId'],
      skip,
      take,
    });

    const total = await this.prisma.campaignView.findMany({
      include: {
        user: {
          select: {
            identities: {
              where: {
                active: true,
                type: IdentityType.TWITTER,
                deletedAt: null,
              },
            },
            id: true,
            name: true,
            email: true,
          },
        },
        userShare: {
          select: {
            email: true,
            identities: {
              where: {
                active: true,
                type: IdentityType.TWITTER,
                deletedAt: null,
              },
            },
            id: true,
            name: true,
          },
        },
      },
      where: {
        campaignId,
        userShareId: {
          not: null,
        },
      },
      distinct: ['userShareId'],
    });

    for (const user of campaignViews) {
      const sharedUrlsWithLinkVisitsCount =
        await this.prisma.campaignView.count({
          where: {
            userShareId: user.userShareId,
            campaignId,
          },
        });

      const sharedUrlsWithTasks = await this.prisma.userTask.findMany({
        where: {
          userShareId: user.userShareId,
          task: {
            campaignId,
          },
        },
        distinct: ['userId'],
      });

      let sharedUrlsWithCompletedRequiredTasksCount = 0;

      if (sharedUrlsWithTasks.length) {
        const uniqueUserIds = [
          ...new Set(sharedUrlsWithTasks.map((item) => item.userId)),
        ];
        sharedUrlsWithCompletedRequiredTasksCount = uniqueUserIds.filter(
          (item) => campaignViews.filter((user) => user.userId === item)
        ).length;
      }

      (user['identityAccountName'] =
        user.userShare.identities?.[0]?.accountName ??
        user.userShare?.email?.email),
        (user['sharedUrlsWithLinkVisitsCount'] =
          sharedUrlsWithLinkVisitsCount < sharedUrlsWithTasks.length
            ? sharedUrlsWithTasks.length
            : sharedUrlsWithLinkVisitsCount);
      user['sharedUrlsWithTasks'] = sharedUrlsWithTasks.length;
      user['sharedUrlsWithCompletedRequiredTasksCount'] =
        sharedUrlsWithCompletedRequiredTasksCount;
    }

    res.send({
      data: campaignViews,
      total: total.length,
    });
    res.end();
  }

  async getStatisticsCampaignChart(
    res: Response,
    userId: number,
    campaignId: string,
    query: GetUserCompletedCampaignListParamsDto
  ) {
    const { dateType } = query;
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);

    if (
      (!user?.companyId || user?.companyId !== campaign.companyId) &&
      !user.isSuperAdmin
    )
      throw new NotAcceptableException(NOT_HAVE_PERMISSION);

    const startAt = dayjs(campaign.startTime).startOf('day');
    let endAt = dayjs(campaign.expiredTime);
    const today = dayjs().endOf('day');

    if (!endAt.isValid() || endAt.isBefore(today) || endAt.isSame(today)) {
      endAt = today;
    }
    const dateRange = this.getDateRange(
      startAt.toDate(),
      endAt.toDate(),
      dateType
    );

    const data = {};
    let beforeDate = dateRange[0];
    for (const date of dateRange) {
      let startDate = dayjs(beforeDate).add(1, 'day').startOf('day').toDate();
      const endDate = dayjs(date).endOf('day').toDate();

      if (dayjs(beforeDate).startOf('day').isSame(dayjs(date).startOf('day'))) {
        startDate = dayjs(date).startOf('day').toDate();
      }

      const visitCampaignToday = await this.prisma.campaignView.findMany({
        where: {
          campaignId,
          createdAt: {
            gt: startDate,
            lte: endDate,
          },
        },
        distinct: ['userId'],
      });

      const visitCampaignTotal = await this.prisma.campaignView.findMany({
        where: {
          campaignId,
          createdAt: {
            gte: startAt.toDate(),
            lte: endDate,
          },
        },
        distinct: ['userId'],
      });

      const joinCampaignTotal = await this.prisma.userTask.findMany({
        where: {
          task: {
            campaignId,
          },
          createdAt: {
            gte: startAt.toDate(),
            lte: endDate,
          },
        },
        distinct: ['userId'],
      });

      const userCampaignToday = await this.prisma.userTask.findMany({
        where: {
          task: {
            campaignId,
          },
          createdAt: {
            gt: startDate,
            lte: endDate,
          },
        },
        distinct: ['userId'],
      });

      const checkUserCampaignToday = await this.prisma.userTask.findMany({
        where: {
          task: {
            campaignId,
          },
          userId: {
            in: userCampaignToday?.map((item) => item?.userId),
          },
          createdAt: {
            lte: startDate,
          },
        },
        distinct: ['userId'],
      });

      const joinCampaignToday =
        userCampaignToday?.length - checkUserCampaignToday?.length;
      beforeDate = endDate;

      data[date] = {
        joinCampaignToday: joinCampaignToday,
        joinCampaignTotal: joinCampaignTotal.length,
        visitCampaignToday:
          visitCampaignToday.length < joinCampaignToday
            ? joinCampaignToday
            : visitCampaignToday.length,
        visitCampaignTotal:
          visitCampaignTotal.length < joinCampaignTotal.length
            ? joinCampaignTotal.length
            : visitCampaignTotal.length,
      };
    }

    res.send({
      data,
    });
    res.end();
  }

  async exportUserCompletedCampaignListToCsv(
    res: Response,
    userId: number,
    campaignId: string
  ) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });
    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);
    const columns = [
      'Xアカウント名',
      'メールアドレス',
      '参加日時',
      '自由形式質問_1',
      '自由形式質問_2',
    ];
    if (
      campaign.methodOfselectWinners == MethodOfselectWinners.AUTO_PRIZEE_DRAW
    )
      columns.splice(3, 0, '賞品');
    if (
      campaign.methodOfselectWinners == MethodOfselectWinners.MANUAL_SELECTION
    )
      columns.splice(3, 0, '獲得ポイント');
    const stringifier = stringify({ header: true, columns: columns });
    const userCampaigns = await this.prisma.userCampaign.findMany({
      where: { campaignId: campaign.id },
      include: {
        user: {
          include: {
            email: true,
            UserTask: {
              where: { task: { type: TaskType.CUSTOM, campaignId } },
              orderBy: {
                taskId: 'asc',
              },
            },
          },
        },
        award: {
          include: {
            coupon: true,
          },
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });
    res.setHeader('Content-disposition', 'attachment; filename=export.csv');
    res.setHeader('Content-type', 'application/csv');
    for (const userCampaign of userCampaigns) {
      const taskAnswer = [];
      const userTasks = await this.prisma.userTask.findMany({
        where: {
          userId: userCampaign.userId,
          task: {
            campaignId,
          },
        },
        include: {
          task: {
            include: {
              taskTemplate: true,
            },
          },
        },
      });

      for (const userTask of userTasks.filter(
        (item) => item.task?.type === TaskType.CUSTOM
      )) {
        taskAnswer.push(userTask.answer);
      }
      console.log('--------------------------');
      console.log({ taskAnswer });
      console.log('-----------3465345grdru56567j4 f---------------');
      console.log({ taskAnswer: JSON.stringify(taskAnswer) });
      const date = moment
        .tz(userCampaign.createdAt, 'Asia/Tokyo')
        .format('YYYY-MM-DD HH:mm');

      const points =
        userTasks
          ?.map((item) => item.task?.taskTemplate?.points ?? 0)
          .reduce((acc, curr) => acc + +(curr ?? 0), 0) ?? 0;

      const dataImport = [
        userCampaign.identityAccountName,
        userCampaign.user?.email?.email,
        date,
        taskAnswer[0] ?? '',
        taskAnswer[1] ?? '',
      ];

      if (
        campaign.methodOfselectWinners == MethodOfselectWinners.AUTO_PRIZEE_DRAW
      ) {
        let award = '';
        if (!userCampaign?.award) {
          award = 'Not yet applied'
        } else if (userCampaign?.award?.value) {
          award = `Won - ${userCampaign?.award?.value}`
          if(!userCampaign?.award?.coupon) {
            award = `Won - ${userCampaign?.award?.value} but not claimed `
          }
        } else {
          award = 'Lost';
        }

        dataImport.splice(3, 0, award);
      }
      if (
        campaign.methodOfselectWinners == MethodOfselectWinners.MANUAL_SELECTION
      )
        dataImport.splice(3, 0, points ?? '');
      stringifier.write(dataImport);
    }
    stringifier.pipe(res);
    stringifier.end();
  }


  async checkUserLastUpdate(campaignId: string) {
    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });
    return {
      emailUserUpdated: campaign?.emailUserUpdated,
      idUserUpdated: campaign?.idUserUpdated,
      timeUpdated: campaign?.timeUpdated,
    };
  }

  @Cron('*/40 * * * * *')
  async checkTimeCampaign() {
    console.log('call remove_old_data');
    const campaignIdsNeedUpdateStatusToPublic = [];
    const campaignIdsNeedUpdateStatusToCompletion = [];
    const waittingPublicCampaigns = await this.prisma.campaign.findMany({
      where: {
        status: {
          in: [CampaignStatus.WAITING_FOR_PUBLICATION, CampaignStatus.PUBLIC],
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });
    for (const campaign of waittingPublicCampaigns) {
      if (campaign.status == CampaignStatus.WAITING_FOR_PUBLICATION) {
        if (
          new Date(campaign.startTime).getTime() - 10000 <
          new Date().getTime()
        )
          campaignIdsNeedUpdateStatusToPublic.push(campaign.id);
      } else if (campaign.status == CampaignStatus.PUBLIC) {
        if (
          campaign.setExpiredTime &&
          campaign.expiredTime &&
          new Date(campaign.expiredTime).getTime() < new Date().getTime()
        )
          campaignIdsNeedUpdateStatusToCompletion.push(campaign.id);
      }
    }
    if (campaignIdsNeedUpdateStatusToPublic.length > 0)
      await this.prisma.campaign.updateMany({
        where: { id: { in: campaignIdsNeedUpdateStatusToPublic } },
        data: {
          status: CampaignStatus.PUBLIC,
        },
      });
    if (campaignIdsNeedUpdateStatusToCompletion.length > 0)
      await this.prisma.campaign.updateMany({
        where: { id: { in: campaignIdsNeedUpdateStatusToCompletion } },
        data: {
          status: CampaignStatus.COMPLETION,
          endReason: CampaignEndReason.OTHER,
        },
      });

    for (const campaignId of campaignIdsNeedUpdateStatusToCompletion) {
      const campaign = await this.prisma.campaign.findFirst({
        where: { id: campaignId },
      });

      if (campaign) {
        await this.prisma.campaign.update({
          where: { id: campaignId },
          data: {
            status: CampaignStatus.COMPLETION,
            endReason: CampaignEndReason.OTHER,
          },
        });
        await this.paymentsService.calculatePayment(
          campaign.createdUserId,
          campaignId
        );
      }
    }
    return true;
  }

  async shareForX(data: ShareUrlDto) {
    try {
      const { url, campaignName } = data
      const twitter = new TwitterApi({
        appKey: process.env.TWITTER_API_KEY,
        appSecret: process.env.TWITTER_API_KEY_SECRET,
        accessToken: process.env.TWITTER_ACCESS_TOKEN,
        accessSecret: process.env.TWITTER_ACCESS_TOKEN_SECRET,
      });

      const bearer = new TwitterApi(process.env.TWITTER_BEARER_TOKEN);

      // const twitterClient = twitter.readWrite;
      // const twitterBearer = bearer.readOnly;
      await twitter.v2.tweet(`"${campaignName}"の当選者"一覧: ${url}.`)
      await twitter.v2.tweet(`Danh sách người trúng thưởng của ${campaignName}: ${url}.`)

    } catch (e) {
      console.log(e)
    }
  }

  async selectWinnerManually(campaignId: string, data: WinnerManuallyDto) {
    try {
      const { numberOfWinners, selectionType } = data

      const listUser: canParticipateInDraw[] = []
      const campaign = await this.prisma.campaign.findFirst({
        where: {
          id: campaignId,
          methodOfselectWinners: 'MANUAL_SELECTION',
          status: "COMPLETION",
        }
      })

      const checkWinners = await this.prisma.userAward.findFirst({
        where: {
          userCampaign: {
            campaignId
          }
        }
      })

      if (!campaign) {
        throw new NotFoundException(CAMPAIGN_NOT_FOUND);
      }

      const userCampaigns = await this.prisma.userCampaign.findMany({
        where: {
          campaignId,
        },
      })

      for (const userCampaign of userCampaigns) {
        const userTasks = await this.prisma.userTask.findMany({
          where: {
            userId: userCampaign.userId,
            task: {
              campaignId: userCampaign.campaignId
            }
          },
          include: {
            user: {
              include: {
                identities: true,
                email: true
              }
            },
            task: {
              include: {
                taskTemplate: true,
              }
            },
          }
        })

        if (userTasks.length) {
          const points = userTasks
            ?.map((item) => item.task?.taskTemplate?.points ?? 0)
            .reduce((acc, curr) => acc + +(curr ?? 0), 0) ?? 0

          listUser.push({
            userCampaignId: userCampaign.id,
            userId: userCampaign.userId,
            email: userTasks[0]?.user?.email?.email,
            identityAccountName: userCampaign.identityAccountName,
            points,
          })
        }
      }

      let result: canParticipateInDraw[] = []
      if (listUser.length) {
        if (selectionType === 'SELECT_BY_POINTS') {
          listUser.sort((a, b) => b.points - a.points);
          result = listUser.slice(0, numberOfWinners)
        } else if (selectionType === 'RANDOM_EQUAL_RATIO') {
          result = this.getFairRandom(listUser, numberOfWinners);
        } else {
          result = this.getWeightedRandom(listUser, numberOfWinners)
        }
      }

      if (checkWinners) {
        await this.prisma.userAward.updateMany({
          where: {
            userCampaign: {
              campaignId,
              id: {
                notIn: result.map((item) => item.userCampaignId)
              }
            },
            deletedAt: null
          },
          data: {
            isWin: "false",
          }
        })
      }

      await this.prisma.userAward.updateMany({
        data: {
          isWin: "true",
        },
        where: {
          userCampaign: {
            campaignId,
            id: {
              in: result.map((item) => item.userCampaignId)
            }
          },
          deletedAt: null
        },
      })

      return result
    } catch (error) {
      console.log(error)
      throw new BadRequestException(error)
    }
  }

  getFairRandom(arr: canParticipateInDraw[], n: number): canParticipateInDraw[] {
    if (n >= arr.length) return arr;

    // Trộn danh sách
    const shuffled = this.shuffle(arr.slice())

    // Lấy n phần tử đầu tiên
    return shuffled.slice(0, n);
  }

  shuffle(array: canParticipateInDraw[]): canParticipateInDraw[] {
    let currentIndex = array.length;
    let randomIndex = 0;

    while (currentIndex !== 0) {
      // Chọn một phần tử ngẫu nhiên từ phần tử hiện tại trở về đầu mảng
      randomIndex = Math.floor(Math.random() * currentIndex);
      currentIndex--;

      // Hoán đổi phần tử hiện tại với phần tử ngẫu nhiên
      [array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];
    }

    return array;
  }

  getWeightedRandom(arr: canParticipateInDraw[], n: number): canParticipateInDraw[] {
    if (n >= arr.length) return arr;

    // Tạo danh sách mở rộng với trọng số
    const weightedList: canParticipateInDraw[] = [];
    arr.forEach(item => {
      for (let i = 0; i < item.points; i++) {
        weightedList.push(item);
      }
    });

    // Xáo trộn danh sách mở rộng và lấy n phần tử duy nhất
    const shuffled = this.shuffle(weightedList)
    const uniqueItems = new Set<number>();
    const result: canParticipateInDraw[] = [];

    for (const item of shuffled) {
      if (uniqueItems.size >= n) break; // Dừng khi đủ số lượng phần tử duy nhất
      if (!uniqueItems.has(item.userId)) {
        uniqueItems.add(item.userId);
        result.push(item);
      }
    }

    return result;
  }



  async getUserWin(campaignId: string, query: GetIsWinParamsDto) {
    const { skip, take, identityAccountName } = query
    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });

    if (!campaign) {
      throw new NotFoundException(CAMPAIGN_NOT_FOUND);
    }

    const users = await this.prisma.userAward.findMany({
      include: {
        userCampaign: true,
      },
      where: {
        userCampaign: {
          campaignId
        },
        isWin: 'true',
        deletedAt: null,
      },
      orderBy: {
        value: "desc"
      },
      skip,
      take
    })


    const total = await this.prisma.userAward.count({
      where: {
        userCampaign: {
          campaignId
        },
        isWin: 'true',
        deletedAt: null,
      }
    })


    return {
      data: {
        users,
        campaign
      },
      total
    }
  }

  // private transformMulterImageData(image: Express.Multer.File) {
  //   const baseUrl = this.configService.get<string>('meta.appUrl');
  //   return `${baseUrl}/${image.path}`;
  // }

  private async transformMulterImageData(
    userId: number,
    image: Express.Multer.File
  ) {
    const date = new Date().toISOString().replace(/-|\.|:/g, '');
    const managementCode = `${userId}_${date}`;
    const randomName = Array(16)
      .fill(null)
      .map(() => Math.round(Math.random() * 16).toString(16))
      .join('');
    const bucketName =
      this.configService.get<Configuration['s3']['bucketName']>(
        's3.bucketName'
      );
    const uploadFileParams = {
      name: managementCode + '-' + randomName + '-' + image.originalname,
      body: image.buffer,
      bucket: bucketName,
    };
    const uploadedImage = await this.s3Service.upload(uploadFileParams);

    return uploadedImage.Location;
  }

  getDateRange(
    startDate: Date,
    endDate: Date,
    type: 'day' | 'week' | 'month' | 'year' = 'week'
  ) {
    const start = dayjs(startDate);
    const end = dayjs(endDate);
    const range = [];

    let current = start;
    while (current.isBefore(end, type) || current.isSame(end, type)) {
      if (current.isSame(end, type)) {
        range.push(end.format('YYYY-MM-DD'));
      } else {
        range.push(current.format('YYYY-MM-DD'));
      }

      current = current.add(1, type);
    }
    return range;
  }
}
