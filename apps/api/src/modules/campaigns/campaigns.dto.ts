import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CampaignStatus, MethodOfselectWinners } from '@prisma/client';
import { Type } from 'class-transformer';
import {
  IsDate,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUrl,
} from 'class-validator';
import { ListRequest } from '../../utils/requests.common';

export class CreateCampaignDraftDto {
  @ApiPropertyOptional({ type: 'string', example: 'campaignName' })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({ type: 'string', example: 'category' })
  @IsString()
  @IsOptional()
  category?: string;

  @ApiPropertyOptional({ type: 'string', example: 'description' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ type: 'string', example: '2023-02-28T09:00:17.409Z' })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  startTime?: Date;

  @ApiPropertyOptional({ type: 'string', example: '2023-02-28T09:00:17.409Z' })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  expiredTime?: Date;

  @ApiPropertyOptional({ type: 'string', example: 'false' })
  @IsOptional()
  @IsString()
  setExpiredTime?: string;

  @ApiPropertyOptional({
    enum: MethodOfselectWinners,
    example: 'AUTO_PRIZEE_DRAW',
  })
  @IsEnum(MethodOfselectWinners)
  @IsOptional()
  methodOfselectWinners?: MethodOfselectWinners;

  @ApiPropertyOptional({ type: 'string', example: '10' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalNumberOfUsersAllowedToWork?: number;

  @ApiPropertyOptional({ type: 'string', example: '20' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfPrizes?: number;

  @ApiPropertyOptional({ type: 'string', example: '30' })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalPrizeValue?: number;

  @ApiPropertyOptional({ type: 'string', example: 'note' })
  @IsOptional()
  @IsString()
  noteReward?: string;

  @ApiPropertyOptional({ type: 'string', example: 'true' })
  @IsOptional()
  @IsString()
  settingForNotWin?: string;

  @ApiPropertyOptional({ type: 'string', example: 'true' })
  @IsOptional()
  @IsString()
  isWaitingPurcare?: string;

  @ApiPropertyOptional({ type: 'string' })
  @IsOptional()
  campaignImage?: Express.Multer.File;

  @ApiPropertyOptional({
    enum: CampaignStatus,
  })
  @IsEnum(CampaignStatus)
  @IsOptional()
  status?: CampaignStatus;
}

export class UpdateCampaignDto extends CreateCampaignDraftDto {}

export class GetCampaignListParamsDto extends ListRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  except?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsEnum(CampaignStatus)
  status?: CampaignStatus;
}

export class GetIsWinParamsDto extends ListRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  identityAccountName?: string;
}

export class WinnerManuallyDto {
  @ApiProperty()
  @IsNumber()
  numberOfWinners: number;

  @ApiPropertyOptional({
    enum: ['RANDOM_EQUAL_RATIO', 'RANDOM_WEIGHTED_BY_POINTS', 'SELECT_BY_POINTS'],
    default: 'RANDOM_EQUAL_RATIO',
  })
  @IsString()
  selectionType: 'RANDOM_EQUAL_RATIO' | 'RANDOM_WEIGHTED_BY_POINTS' | 'SELECT_BY_POINTS';
}

export class GetUserCompletedCampaignListParamsDto extends ListRequest {
  @ApiPropertyOptional({ enum: ['list', 'csv', 'chart'], default: 'list' })
  @IsString()
  action: 'list' | 'csv' | 'chart' = 'list';

  @ApiPropertyOptional()
  @IsOptional()
  @IsDate()
  startAt?: Date;

  @ApiPropertyOptional()
  @IsOptional()
  @IsDate()
  endAt?: Date;

  @ApiPropertyOptional({
    enum: ['day', 'week', 'month', 'year'],
    default: 'week',
  })
  @IsOptional()
  dateType?: 'day' | 'week' | 'month' | 'year' = 'week';

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  skip?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  take?: number;

  @ApiPropertyOptional()
  @IsOptional()
  sort?: string

  @ApiPropertyOptional()
  @IsOptional()
  field?: string
}

export class GetDetailsCampaignDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  currentId?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  shareToken?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  isAdmin?: string;

  @ApiPropertyOptional()
  @IsOptional()
  sort?: string

  @ApiPropertyOptional()
  @IsOptional()
  field?: string
}
export class ShareUrlDto {
  @ApiProperty()
  @IsString()
  @IsUrl()
  url: string;

  @ApiProperty()
  @IsString()
  campaignName: string;
}

