import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  NotImplementedException,
  Param,
  Post,
  Put,
  Query,
  Res,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { CampaignsService } from './campaigns.service';
import { CurrentUser } from '../../helpers/current-user-decorator';
import { AccessTokenParsed } from '../auth/auth.interface';
import {
  CreateCampaignDraftDto,
  GetCampaignListParamsDto,
  GetDetailsCampaignDto,
  GetIsWinParamsDto,
  GetUserCompletedCampaignListParamsDto,
  ShareUrlDto,
  UpdateCampaignDto,
  WinnerManuallyDto,
} from './campaigns.dto';
import { FileInterceptor } from '@nestjs/platform-express';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { diskStorage } from 'multer';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { Public } from '../auth/public.decorator';
import { RateLimit } from '../auth/rate-limit.decorator';

@Controller('campaigns')
@ApiTags('campaignsAPI')
export class CampaignsController {
  constructor(private readonly campaignsService: CampaignsService) { }

  @Post()
  @UseInterceptors(FileInterceptor('campaignImage'))
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: AccessTokenParsed,
    @Body() createCampaignDto: CreateCampaignDraftDto,
    @UploadedFile() campaignImage: Express.Multer.File
  ) {
    return await this.campaignsService.create(user.id, {
      ...createCampaignDto,
      campaignImage,
    });
  }

  @RateLimit(1)
  @Get()
  async list(
    @CurrentUser() user: AccessTokenParsed,
    @Query() query: GetCampaignListParamsDto
  ) {
    return await this.campaignsService.list(
      query,
      user?.id !== 0 ? user.id : null
    );
  }

  @Get(':campaignId')
  async details(
    @CurrentUser() user: AccessTokenParsed,
    @Param('campaignId') campaignId: string,
    @Query() query: GetDetailsCampaignDto
  ) {
    return await this.campaignsService.details(
      campaignId,
      query,
      user?.id !== 0 ? user.id : null
    );
  }

  @Put(':campaignId')
  @UseInterceptors(FileInterceptor('campaignImage'))
  async update(
    @CurrentUser() user: AccessTokenParsed,
    @Param('campaignId') campaignId: string,
    @Body() updateCampaignDto: UpdateCampaignDto,
    @UploadedFile() campaignImage: Express.Multer.File
  ) {
    return await this.campaignsService.update(user?.id, campaignId, {
      ...updateCampaignDto,
      campaignImage,
    });
  }

  @Delete(':campaignId')
  async delete(
    // @CurrentUser() user: AccessTokenParsed,
    @Param('campaignId') campaignId: string,
    @Body('isTest') isTest: boolean
  ) {
    return await this.campaignsService.delete(campaignId, isTest);
  }

  @Get(':campaignId/users')
  async getUserCompletedCampaign(
    @Res() res: Response,
    @CurrentUser() user: AccessTokenParsed,
    @Param('campaignId') campaignId: string,
    @Query() query: GetUserCompletedCampaignListParamsDto
  ) {
    if (
      query.action !== 'chart' &&
      query.action !== 'list' &&
      query.action !== 'csv'
    ) {
      throw new NotImplementedException('Action not implemented');
    }

    if (query.action == 'csv') {
      return this.campaignsService.exportUserCompletedCampaignListToCsv(
        res,
        user?.id,
        campaignId
      );
    }

    if (query.action == 'chart') {
      return await this.campaignsService.getUserCompleteCampaignChart(
        res,
        user?.id,
        campaignId,
        query
      );
    } else {
      return await this.campaignsService.getUserCompletedCampaign(
        res,
        user?.id,
        campaignId,
        query
      );
    }
  }

  @Get(':campaignId/statistics')
  async getStatisticsCampaign(
    @Res() res: Response,
    @CurrentUser() user: AccessTokenParsed,
    @Param('campaignId') campaignId: string,
    @Query() query: GetUserCompletedCampaignListParamsDto
  ) {
    if (
      query.action !== 'chart' &&
      query.action !== 'list' &&
      query.action !== 'csv'
    ) {
      throw new NotImplementedException('Action not implemented');
    }

    if (query.action === 'list') {
      return await this.campaignsService.getStatisticsCampaign(
        res,
        user?.id,
        campaignId,
        query
      );
    }

    if (query.action === 'chart') {
      return await this.campaignsService.getStatisticsCampaignChart(
        res,
        user?.id,
        campaignId,
        query
      );
    }
  }

  @Public()
  @Get(':campaignId/check')
  async checkUserLastUpdate(@Param('campaignId') campaignId: string) {
    return await this.campaignsService.checkUserLastUpdate(campaignId);
  }

  @Public()
  @Get(':campaignId/isWin')
  async getUserWin(@Param('campaignId') campaignId: string, @Query() query: GetIsWinParamsDto) {
    return await this.campaignsService.getUserWin(campaignId, query);
  }

  @Public()
  @Post('share')
  async test(@Body() data: ShareUrlDto) {
    return this.campaignsService.shareForX(data);
  }

  @Public()
  @Post(':campaignId/select-winner')
  async selectWinnerManually(@Param('campaignId') campaignId: string, @Body() data: WinnerManuallyDto) {
    return await this.campaignsService.selectWinnerManually(campaignId, data);
  }
}
