import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Length,
  MinLength,
} from 'class-validator';

export class RegisterEmailDto {
  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  email!: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  password!: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  code!: string;
}

export class ForgotPasswordDto {
  @ApiProperty()
  @IsEmail()
  @IsNotEmpty()
  email!: string;
}

export class ResetPasswordDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  token!: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @MinLength(8)
  password!: string;
}

export class LoginDto {
  @ApiPropertyOptional()
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @MinLength(8)
  password?: string;
}
export class TotpLoginDto {
  @IsString()
  @IsNotEmpty()
  token!: string;

  @IsString()
  @IsNotEmpty()
  code!: string;
}

export class VerifyEmailDto {
  @IsString()
  @IsNotEmpty()
  token!: string;
}

export class VerifyTotpDto {
  @ApiPropertyOptional({ type: 'string' })
  @IsString()
  @IsOptional()
  token?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  sendBy?: string;

  @ApiPropertyOptional({ type: 'string' })
  @IsString()
  @IsOptional()
  secret?: string;

  @ApiPropertyOptional({ type: 'string' })
  @IsString()
  @IsOptional()
  @Length(10, 11)
  phoneNumber?: string;

  @ApiPropertyOptional({ type: 'number' })
  @IsNumber()
  @IsOptional()
  userId?: number;

  @ApiPropertyOptional({ type: 'boolean' })
  @IsBoolean()
  @IsOptional()
  isCheckPhone?: boolean;

  @ApiPropertyOptional({ type: 'boolean' })
  @IsBoolean()
  @IsOptional()
  isTest?: boolean;
}
