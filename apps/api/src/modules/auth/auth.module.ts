import { Modu<PERSON> } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { PrismaModule } from '@clout/prisma/prisma.module';
import { GeolocationModule } from '@clout/common/providers/geolocation/geolocation.module';
import { TokensModule } from '@clout/common/providers/tokens/tokens.module';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule } from '@nestjs/config';
import { StaartStrategy } from './staart.strategy';
import { NovuModule } from '@clout/common/providers/novu/novu.module';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    GeolocationModule,
    PrismaModule,
    TokensModule,
    ConfigModule,
    NovuModule,
  ],
  providers: [AuthService, StaartStrategy],
  controllers: [AuthController],
})
export class AuthModule {}
