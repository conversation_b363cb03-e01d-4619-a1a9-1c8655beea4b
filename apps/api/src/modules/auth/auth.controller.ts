import {
  Body,
  Controller,
  Headers,
  HttpCode,
  HttpStatus,
  Ip,
  Post,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { RateLimit } from './rate-limit.decorator';
import { Public } from '../auth/public.decorator';
import { TokenResponse } from './auth.interface';
import {
  ForgotPasswordDto,
  LoginDto,
  RegisterEmailDto,
  ResetPasswordDto,
  TotpLoginDto,
  VerifyTotpDto,
} from './auth.dto';
import { ApiTags } from '@nestjs/swagger';
import { NovuApiKey } from '../../helpers/current-NovuApiKey';

@Controller('auth')
@ApiTags('authAPI')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @Post('login')
  @HttpCode(HttpStatus.OK)
  async loginEmail(
    @Body() data: LoginDto,
    @Ip() ip: string,
    @Headers('User-Agent') userAgent: string
  ): Promise<object> {
    return await this.authService.loginEmail(data, ip, userAgent);
  }

  @Public()
  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  async registerEmail(
    @Body() data: RegisterEmailDto,
    @NovuApiKey() novuApiKey: string,
    @Ip() ip: string,
    @Headers('User-Agent') userAgent: string
  ): Promise<object> {
    return this.authService.registerEmail(data, ip, userAgent, novuApiKey);
  }

  @Public()
  @Post('sms')
  @HttpCode(HttpStatus.OK)
  async verifySMS(
    @Body() data: TotpLoginDto,
    @Ip() ip: string,
    @Headers('User-Agent') userAgent: string
  ): Promise<object> {
    return this.authService.verifySMS(data, ip, userAgent);
  }

  @Public()
  @Post('verification')
  async Verification(@Body() data: VerifyTotpDto): Promise<object> {
    if (data?.type == 'SMS') {
      return this.authService.sendSMS(data);
    } else {
      return this.authService.verifyRecaptcha(data?.token);
    }
  }

  @Public()
  @Post('logout')
  async logout(
    @Body('token') refreshToken: string
  ): Promise<{ success: true }> {
    await this.authService.logout(refreshToken);
    return { success: true };
  }

  @Public()
  @Post('refresh')
  async refresh(
    @Ip() ip: string,
    @Headers('User-Agent') userAgent: string,
    @Body('token') refreshToken: string
  ): Promise<TokenResponse> {
    return this.authService.refresh(ip, userAgent, refreshToken);
  }

  /** Send a password reset link */
  @Public()
  @Post('forgot_password')
  async forgotPassword(
    @Body() data: ForgotPasswordDto,
    @NovuApiKey() novuApiKey: string
  ) {
    return this.authService.requestPasswordReset(data.email, novuApiKey);
  }

  @Public()
  @Post('verify_mail')
  async verifyMail(
    @Body() data: { email: string; userId?: number; isTest?: boolean },
    @NovuApiKey() novuApiKey: string
  ) {
    return this.authService.verifyMail(
      novuApiKey,
      data.email,
      data?.userId,
      data?.isTest
    );
  }

  /** Reset password */
  @Public()
  @Post('reset_password')
  async resetPassword(
    @Ip() ip: string,
    @Headers('User-Agent') userAgent: string,
    @Body() data: ResetPasswordDto
  ): Promise<object> {
    return this.authService.resetPassword(
      ip,
      userAgent,
      data.token,
      data.password
    );
  }
}
