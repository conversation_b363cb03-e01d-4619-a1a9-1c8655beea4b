import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Request, Response } from 'express';
import { Strategy } from 'passport-strategy';
import { PrismaService } from '@clout/prisma/prisma.service';
import { LOGIN_ACCESS_TOKEN } from '@clout/common/providers/tokens/tokens.constants';
import { TokensService } from '@clout/common/providers/tokens/tokens.service';
import { AccessTokenClaims, AccessTokenParsed } from './auth.interface';

class StaartStrategyName extends Strategy {
  name = 'staart';
}

@Injectable()
export class StaartStrategy extends PassportStrategy(StaartStrategyName) {
  constructor(
    private tokensService: TokensService,
    private prisma: PrismaService
  ) {
    super();
  }

  private safeSuccess(result: AccessTokenParsed) {
    return this.success(result);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async authenticate(request: Request, response: Response) {
    /** Bearer JWT authorization && cookies*/
    let token = request.query['token'] ?? request.headers.authorization;
    console.log({ token });

    if (token === 'user') {
      return this.safeSuccess({
        type: 'user',
        id: 0,
        scopes: [],
        sessionId: 0,
      });
    }
    if (typeof token !== 'string') {
      return this.fail('No token found', 401);
    }
    if (token.startsWith('Bearer ')) token = token.replace('Bearer ', '');
    try {
      const payload = this.tokensService.verify(
        LOGIN_ACCESS_TOKEN,
        token
      ) as AccessTokenClaims;
      const { id, scopes, sessionId } = payload;
      const user = await this.prisma.user.findUnique({
        where: { id },
      });
      if (user.deleteFlg) {
        return this.fail('User blocked from service', 401);
      }
      const session = await this.prisma.session.findFirst({
        where: { id: sessionId },
      });
      if (!session) return this.fail('Session expired', 401);
      return this.safeSuccess({
        type: 'user',
        id,
        scopes,
        sessionId,
      });
    } catch (error) {
      return this.fail('Invalid token', 401);
    }
  }
}
