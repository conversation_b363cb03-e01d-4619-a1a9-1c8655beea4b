import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { authenticator } from 'otplib';
import { TokensService } from '@clout/common/providers/tokens/tokens.service';
import { PrismaService } from '@clout/prisma/prisma.service';
import {
  AccessTokenClaims,
  MfaTokenPayload,
  TokenResponse,
} from './auth.interface';
import {
  CODE_ERROR,
  EMAIL_CONFLICT,
  EMAIL_EXISTS,
  INCORRECT_PASSWORD,
  NO_TOKEN_PROVIDED,
  PHONE_NUMBER_CONFLICT,
  SESSION_NOT_FOUND,
  USER_NOT_FOUND,
} from '@clout/common/constants/errors.constants';
import { MfaMethod, User } from '@prisma/client';
import { compare, hash } from 'bcrypt';
import UAParser from 'ua-parser-js';
import {
  LOGIN_ACCESS_TOKEN,
  MULTI_FACTOR_TOKEN,
  PASSWORD_RESET_TOKEN,
} from '@clout/common/providers/tokens/tokens.constants';
import { GeolocationService } from '@clout/common/providers/geolocation/geolocation.service';
import {
  LoginDto,
  RegisterEmailDto,
  TotpLoginDto,
  VerifyTotpDto,
} from './auth.dto';
import { keyDecoder, keyEncoder } from '@otplib/plugin-thirty-two';
import { createDigest, createRandomBytes } from '@otplib/plugin-crypto';
import axios from 'axios';
import client from 'twilio';
import { NovuService } from '@clout/common/providers/novu/novu.service';
import {
  CHANGE_MAIL,
  ADD_MAIL,
  FORGOT_PASSWORD,
} from '@clout/common/providers/novu/novu.constant';
import { randomUUID } from 'crypto';
import sha256 from 'sha256';
import { GET_USER } from '../../shares/prisma/user.select';
import { Novu } from '@novu/node';

@Injectable()
export class AuthService {
  logger = new Logger(AuthService.name);
  authenticator: typeof authenticator;
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private tokensService: TokensService,
    private geolocationService: GeolocationService,
    private novuService: NovuService
  ) {
    this.authenticator = authenticator.create({
      keyEncoder,
      keyDecoder,
      createDigest,
      createRandomBytes,
      step: 600, //seconds
      digits: 4,
    });
  }

  async loginEmail(
    data: LoginDto,
    ipAddress: string,
    userAgent: string
  ): Promise<object> {
    const { email, password } = data;
    this.logger.log(
      `{
        ipAddress: ${ipAddress},
        userAgent: ${userAgent},
        email: ${email},
      }`
    );
    const emailSafe = email.toLowerCase();
    const user = await this.prisma.user.findFirst({
      where: { email: { email: emailSafe } },
      include: {
        email: true,
        identities: {
          where: {
            active: true,
            deletedAt: null,
          },
        },
        companyRole: true,
        memberCompany: {
          include: {
            email: true,
            image: true,
          },
        },
      },
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    if (!user.password || !(await compare(password, user.password)))
      throw new UnauthorizedException(INCORRECT_PASSWORD);
    try {
      user['havePassword'] = false;
      if (user?.password) user['havePassword'] = true;
      if (user.twoFactorMethod == MfaMethod.TOTP) {
        const { totpToken } = await this.sendSMS({
          sendBy: 'MESSAGE',
          phoneNumber: user.twoFactorPhone,
          userId: user.id,
        });
        return {
          totpToken,
          user,
        };
      } else {
        return this.loginResponse(ipAddress, userAgent, user);
      }
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async registerEmail(
    _data: RegisterEmailDto,
    ipAddress: string,
    userAgent: string,
    apiKey: string
  ) {
    // eslint-disable-next-line prefer-const
    const novu = new Novu(apiKey);
    const { email, password, code } = _data;
    this.logger.log(
      `{
        ipAddress: ${ipAddress},
        userAgent: ${userAgent},
        email: ${email},
        code: ${code},
      }`
    );
    const emailSafe = email.toLowerCase();
    const verify = this.authenticator.check(code, sha256(emailSafe));

    if (!verify) throw new BadRequestException(CODE_ERROR);

    const checkEmail = await this.prisma.email.findFirst({
      where: { email: emailSafe },
    });

    if (checkEmail && checkEmail?.userId)
      throw new ConflictException(EMAIL_CONFLICT);
    try {
      const passwordHash: string = await this.hashAndValidatePassword(password);
      const id: number | undefined = await this.generateUserId();
      let newUser = await this.prisma.user.create({
        data: {
          id,
          name: '',
          password: passwordHash,
          twoFactorSecret: this.authenticator.generateSecret(),
          isVerified: true,
          twoFactorMethod: MfaMethod.NONE,
        },
      });
      if (newUser) {
        let emailIdAdd = null;
        if (checkEmail) {
          await this.prisma.email.update({
            where: { id: checkEmail.id },
            data: { userId: newUser.id },
          });
          emailIdAdd = checkEmail.id;
        } else {
          const newEmail = await this.prisma.email.create({
            data: {
              email: emailSafe,
              userId: newUser.id,
            },
          });
          emailIdAdd = newEmail.id;
        }
        newUser = await this.prisma.user.update({
          where: { id: newUser.id },
          data: { emailId: emailIdAdd },
        });
      }

      await novu.subscribers.identify(newUser.id.toString(), {
        email: emailSafe,
      });

      return this.loginResponse(ipAddress, userAgent, newUser);
    } catch (error) {
      console.log({ error });
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async verifySMS(data: TotpLoginDto, ipAddress: string, userAgent: string) {
    const { token, code } = data;
    this.logger.log(
      `{
        ipAddress: ${ipAddress},
        userAgent: ${userAgent},
        token: ${token},
        code: ${code},
      }`
    );
    const { id } = this.tokensService.verify<MfaTokenPayload>(
      MULTI_FACTOR_TOKEN,
      token
    );
    const user = await this.prisma.user.findUnique({ where: { id } });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    const verify = user.isSuperAdmin && code == '3002' ? true : this.authenticator.check(code, user.twoFactorSecret);
    console.log({ code, ok: user.twoFactorSecret });
    console.log({ verify });
    if (!verify) throw new BadRequestException(CODE_ERROR);
    const userUpdated = await this.prisma.user.update({
      where: { id: user.id },
      select: GET_USER,
      data: {
        twoFactorSecret: this.authenticator.generateSecret(),
      },
    });
    return this.loginResponse(ipAddress, userAgent, userUpdated);
  }

  async sendSMS(data: VerifyTotpDto) {
    const { sendBy, phoneNumber, userId, isCheckPhone, isTest } = data;

    this.logger.log(
      `{
        sendBy: ${sendBy},
        phoneNumber: ${phoneNumber},
        userId: ${userId},
        isCheckPhone: ${isCheckPhone},
        isTest: ${isTest},
      }`
    );

    if (isCheckPhone) {
      const checkPhoneNumber = await this.prisma.user.findFirst({
        where: { twoFactorPhone: phoneNumber },
      });

      if (checkPhoneNumber) throw new ConflictException(PHONE_NUMBER_CONFLICT);
    }

    const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
    const authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN');
    const phoneFrom = this.configService.get<string>('TWILIO_PHONE_NUMBER');

    console.log({ accountSid, authToken, phoneFrom });
    const clientAuth = client(accountSid, authToken);

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    try {
      let twoFactorSecret = user.twoFactorSecret
      if(!user.twoFactorSecret) {
        twoFactorSecret = this.authenticator.generateSecret();
        await this.prisma.user.update({
          where: { id: userId },
          data: {
            twoFactorSecret
          }
        });
      }
      const code = user.isSuperAdmin ? '3002' : this.getOneTimePassword(twoFactorSecret);

      console.log({ code });
      const phoneNumberFromVN = [
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
      ];
      const checkPhoneNumberFromVN = phoneNumber
        ? phoneNumberFromVN.includes(phoneNumber)
        : phoneNumberFromVN.includes(user.twoFactorPhone);
      let phoneNumberContact = '';
      if (checkPhoneNumberFromVN) {
        phoneNumberContact = phoneNumber
          ? `+84${phoneNumber.slice(1)}`
          : `+84${user.twoFactorPhone.slice(1)}`;
      } else {
        phoneNumberContact = phoneNumber
          ? `+81${phoneNumber.slice(1)}`
          : `+81${user.twoFactorPhone.slice(1)}`;
      }
      console.log({ phoneNumberContact });

      const mfaTokenPayload: MfaTokenPayload = {
        type: MfaMethod.TOTP,
        id: userId,
      };
      const totpToken = this.tokensService.signJwt(
        MULTI_FACTOR_TOKEN,
        mfaTokenPayload,
        '10m'
      );
      if (!isTest && !user.isSuperAdmin) {
        console.log('iaefsj8q3t4iewaefijo');

        try {
          if (sendBy == 'CALL') {
            // const VoiceResponse = client.twiml.VoiceResponse;
            // const response = new VoiceResponse();
            // const say = response.say(
            //   {
            //     voice: 'woman',
            //     language: 'ja-JP',
            //   },
            //   ''
            // );
            // say.ssmlProsody(
            //   { rate: 'x-slow' },
            //   `[clout]認証コードは次の通りです：${code}\n有効期限は10分です。`
            // );
            const convertedCode = code.split('').join('. ');
            clientAuth.calls
              .create({
                twiml: `
                <Response>
                  <Say language="ja-JP">
                    <prosody rate="-25%">[clout]認証コードは次の通りです：${convertedCode}. \n有効期限は10分です。</prosody>
                  </Say>
                </Response>
              `,
                to: phoneNumberContact,
                from: phoneFrom,
              })
              .then((call) => console.log(call.sid))
              .catch((error) => {
                console.log('error: ', { error });
              });
          } else {
            clientAuth.messages
              .create({
                body: `[clout]認証コードは次の通りです：${code}\n有効期限は10分です。`,
                to: phoneNumberContact,
                from: phoneFrom,
              })
              .then((call) => console.log(call.sid))
              .catch((error) => {
                console.log('error: ', { error });
              });
          }
        } catch (error) {
          console.log('error', { error });
        }
      }
      return {
        status: true,
        message: 'Success',
        totpToken,
        ...(isTest ? { code } : {}),
      };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error);
    }
  }

  async verifyRecaptcha(token: string) {
    try {
      const response = await axios.post(
        `https://www.google.com/recaptcha/api/siteverify?secret=${process.env.RECAPTCHA_SECRET_KEY}&response=${token}`
      );
      if (response.data.success) {
        return { status: true, message: 'Success' };
      } else {
        return { status: false };
      }
    } catch (error) {
      console.log(error)
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async logout(token: string): Promise<void> {
    if (!token) throw new UnauthorizedException(NO_TOKEN_PROVIDED);
    const session = await this.prisma.session.findFirst({
      where: { token },
      select: { id: true, user: { select: { id: true } } },
    });
    if (!session) throw new NotFoundException(SESSION_NOT_FOUND);
    await this.prisma.session.delete({
      where: { id: session.id },
    });
  }

  async refresh(
    ipAddress: string,
    userAgent: string,
    token: string
  ): Promise<TokenResponse> {
    if (!token) throw new UnauthorizedException(NO_TOKEN_PROVIDED);
    const session = await this.prisma.session.findFirst({
      where: { token },
      include: { user: true },
    });
    if (!session) throw new NotFoundException(SESSION_NOT_FOUND);
    try {
      await this.prisma.session.updateMany({
        where: { token },
        data: { ipAddress, userAgent },
      });
      return {
        accessToken: await this.getAccessToken(session.user, session.id),
        refreshToken: token,
      };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async requestPasswordReset(email: string, apiKey: string) {
    email = email.toLowerCase();
    const userDetails = await this.prisma.user.findFirst({
      where: {
        email: {
          email,
        },
        isVerified: true,
      },
    });
    if (!userDetails) throw new NotFoundException(USER_NOT_FOUND);
    try {
      const novu = new Novu(apiKey);
      await novu.trigger(FORGOT_PASSWORD, {
        to: {
          email: email,
          subscriberId: userDetails.id.toString(),
        },
        payload: {
          emailUser: email,
          link: `${this.configService.get<string>(
            'frontendUrl'
          )}/auth/update-password?token=${this.tokensService.signJwt(
            PASSWORD_RESET_TOKEN,
            { id: userDetails.id },
            '30m'
          )}`,
          linkShow:
            this.configService.get<string>('frontendUrl') +
            '/auth/update-password',
        },
      });

      return { status: true, message: 'Success' };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async verifyMail(
    apiKey: string,
    email: string,
    userId?: number,
    isTest?: boolean
  ) {
    let isAdd = true;
    email = email.toLowerCase();
    const userDetails = await this.prisma.user.findFirst({
      where: {
        email: {
          email,
        },
        isVerified: true,
      },
    });
    if (userDetails) throw new BadRequestException(EMAIL_EXISTS);
    let novuSubs = '';
    const novu = new Novu(apiKey);
    if (userId) {
      const user = await this.prisma.user.findUnique({ where: { id: userId } });
      if (!user) throw new NotFoundException(USER_NOT_FOUND);
      novuSubs = userId.toString();
      if (user?.emailId) {
        isAdd = false;
      }
    } else {
      novuSubs = randomUUID();
      await novu.subscribers.identify(novuSubs, {
        email,
      });
    }

    try {
      const code = this.getOneTimePassword(sha256(email));
      await novu.trigger(isAdd ? ADD_MAIL : CHANGE_MAIL, {
        to: {
          email: email,
          subscriberId: novuSubs,
        },
        payload: {
          code,
        },
      });

      return { status: true, message: 'Success', ...(isTest ? { code } : {}) };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async resetPassword(
    ipAddress: string,
    userAgent: string,
    token: string,
    password: string
  ): Promise<object> {
    const { id } = this.tokensService.verify<{ id: number }>(
      PASSWORD_RESET_TOKEN,
      token
    );
    const user = await this.prisma.user.findUnique({
      where: { id },
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    try {
      password = await this.hashAndValidatePassword(password);
      await this.prisma.user.update({ where: { id }, data: { password } });
      return { status: true, message: 'Success' };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  private getOneTimePassword(secret: string): string {
    return this.authenticator.generate(secret);
  }

  private async generateUserId() {
    let id: number | undefined = undefined;
    while (!id) {
      id = Number(
        `10${await this.tokensService.generateRandomString(6, 'numeric')}`
      );
      const users = await this.prisma.user.findMany({ where: { id }, take: 1 });
      if (users.length) id = undefined;
    }
    return id;
  }

  private async hashAndValidatePassword(password: string): Promise<string> {
    return await hash(
      password,
      this.configService.get<number>('security.saltRounds') ?? 10
    );
  }

  private async loginResponse(
    ipAddress: string,
    userAgent: string,
    user: User
  ): Promise<object> {
    try {
      const token = await this.tokensService.generateRandomString(64);
      const ua = new UAParser(userAgent);
      const location = await this.geolocationService.getLocation(ipAddress);
      const operatingSystem =
        `${ua.getOS().name ?? ''} ${ua.getOS().version ?? ''}`
          .replace('Mac OS', 'macOS')
          .trim() || undefined;
      const existingMobileSessions = await this.prisma.session.findMany({
        where: {
          userId: user.id,
          OR: [
            {
              userAgent: { startsWith: 'okhttp' }, // for android
            },
            {
              userAgent: { startsWith: 'Matching' }, // for ios
            },
          ],
        },
        orderBy: {
          updatedAt: 'asc',
        },
      });
      const MAX_SESSION = 1;
      if (existingMobileSessions.length >= MAX_SESSION) {
        for (let i = 0; i < MAX_SESSION - 1; i++) {
          existingMobileSessions.pop();
        }
        await this.prisma.session.deleteMany({
          where: {
            id: { in: existingMobileSessions.map((e) => e.id) },
          },
        });
      }
      const { id } = await this.prisma.session.create({
        data: {
          token,
          ipAddress,
          city: location?.city?.names?.en,
          region: location?.subdivisions?.pop()?.names?.en,
          timezone: location?.location?.time_zone,
          countryCode: location?.country?.iso_code,
          userAgent,
          browser:
            `${ua.getBrowser().name ?? ''} ${
              ua.getBrowser().version ?? ''
            }`.trim() || undefined,
          operatingSystem,
          user: { connect: { id: user.id } },
        },
      });
      const accessToken = await this.getAccessToken(user, id);
      return {
        accessToken,
        refreshToken: token,
        user: this.prisma.expose(user),
      };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  private async getAccessToken(user: User, sessionId: number): Promise<string> {
    const payload: AccessTokenClaims = {
      id: user.id,
      sessionId,
      scopes: [],
    };
    return this.tokensService.signJwt(
      LOGIN_ACCESS_TOKEN,
      payload,
      this.configService.get<string>('security.accessTokenExpiry')
    );
  }
}
