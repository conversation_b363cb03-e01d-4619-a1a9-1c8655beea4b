import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CampaignRewardType } from '@prisma/client';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { ListRequest } from '../../utils/requests.common';

export class CreateRewardDto {
  @ApiProperty()
  @IsEnum(CampaignRewardType)
  type: CampaignRewardType;

  @ApiProperty()
  @IsNumber()
  index: number;

  @ApiProperty()
  @IsNumber()
  amountOfMoney: number;

  @ApiProperty()
  @IsNumber()
  numberOfWinningTicket: number;
}

export class UpdateRewardDto extends CreateRewardDto {
  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  rewardId?: number;
}

export class GetRewardListParamsDto extends ListRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  campaignId?: string;
}
