import {
  CAMPAIGN_EXPIRED,
  CAMPAIGN_NOT_FOUND,
  COMPANY_NOT_FOUND,
  HAVE_ALREADLY_RECEIVED,
  HAVE_NOT_COMPLETED_CAMPAIGN,
  REWARD_NOT_FOUND,
  UNAUTHORIZED_RESOURCE,
  USER_NOT_FOUND,
} from '@clout/common/constants/errors.constants';
import { PrismaService } from '@clout/prisma/prisma.service';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateRewardDto,
  GetRewardListParamsDto,
  UpdateRewardDto,
} from './rewards.dto';
import { CampaignEndReason, CampaignStatus, MethodOfselectWinners } from '@prisma/client';

@Injectable()
export class RewardsService {
  logger = new Logger(RewardsService.name);
  constructor(private prisma: PrismaService) { }

  async list(query: GetRewardListParamsDto) {
    let rewards = [];
    let total = 0;
    const { skip, take, orderBy, campaignId } = query;
    if (campaignId) {
      const campaign = await this.prisma.campaign.findUnique({
        where: {
          id: campaignId,
        },
        include: { CampaignReward: true },
      });
      if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);
    }
    try {
      [rewards, total] = await this.prisma.$transaction([
        this.prisma.campaignReward.findMany({
          ...(skip ? { skip } : {}),
          ...(take ? { take } : {}),
          where: { campaignId },
          include: {
            campaign: true,
            userAward: {
              include: {
                userCampaign: {
                  include: {
                    user: {
                      include: {
                        email: true,
                      },
                    },
                  },
                },
              },
            },
          },
          ...(orderBy
            ? { orderBy }
            : {
              orderBy: {
                id: 'asc',
              },
            }),
        }),

        this.prisma.campaignReward.count({ where: { campaignId } }),
      ]);

      return { rewards, total };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async create(
    userId: number,
    campaignId: string,
    createRewardDto: CreateRewardDto[]
  ) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { companyRole: true },
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    if ((!user.companyId || !user?.companyRole?.isVerified) && !user.isSuperAdmin)
      throw new NotFoundException(COMPANY_NOT_FOUND);
    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });
    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);
    try {
      return await this.prisma.$transaction(async (prisma) => {
        const newRewardsData = [];
        for (const reward of createRewardDto) {
          const newReward = await prisma.campaignReward.create({
            data: {
              campaign: {
                connect: { id: campaign.id },
              },
              ...reward,
            },
          });
          newRewardsData.push(newReward);
        }
        return newRewardsData;
      });
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async update(
    userId: number,
    campaignId: string,
    updateRewardDto: UpdateRewardDto[]
  ) {

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { companyRole: true },
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });
    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);
    if (
      campaign?.companyId !== user?.companyId &&
      (!user.companyId || !user?.companyRole?.isVerified) && !user.isSuperAdmin
    )
      throw new ForbiddenException(UNAUTHORIZED_RESOURCE);
    try {
      return await this.prisma.$transaction(async (prisma) => {
        const newTasksData = [];
        for (const reward of updateRewardDto) {
          const { rewardId, ...updateData } = reward;
          const rewardCheck = await this.prisma.campaignReward.findUnique({
            where: { id: rewardId },
          });
          if (!rewardCheck) throw new NotFoundException(REWARD_NOT_FOUND);

          const updateReward = await prisma.campaignReward.update({
            where: { id: rewardId },
            data: {
              campaignId: campaign.id,
              ...updateData,
            },
          });

          newTasksData.push(updateReward);
        }
        return newTasksData;
      });
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async delete(userId: number, campaignId: string, rewardIds: number[]) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { companyRole: true },
    });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);
    if (
      campaign?.companyId !== user?.companyId &&
      (!user.companyId || !user?.companyRole?.isVerified) && !user.isSuperAdmin
    )
      throw new ForbiddenException(UNAUTHORIZED_RESOURCE);

    try {
      if (rewardIds.length > 0) {
        await this.prisma.campaignReward.deleteMany({
          where: {
            id: { in: rewardIds },
          },
        });
      }
      return { status: true, message: 'Success' };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async userGacha(userId: number, campaignId: string) {
    this.logger.log({
      userId,
      campaignId
    })
    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
      include: {
        UserClaimCampaign: {
          include: {
            award: true,
          },
          distinct: ['userId']
        },
        CampaignReward: {
          include: { userAward: true },
        },
        Task: {
          include: {
            taskTemplate: true,
          },
        },
      },
    });

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        UserCampaign: {
          where: { campaignId: campaign.id },
          include: {
            award: {
              include: { coupon: true, campaignReward: true },
            },
          },
          distinct: ['userId']
        },
      },
    });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    if (
      campaign?.status !== CampaignStatus.PUBLIC ||
      (campaign.expiredTime &&
        new Date(campaign.expiredTime.toString()).getTime() <=
        new Date().getTime())
    ) throw new BadRequestException(CAMPAIGN_EXPIRED);

    if (user.UserCampaign.length == 0) throw new BadRequestException(HAVE_NOT_COMPLETED_CAMPAIGN);

    if (user.UserCampaign[0]?.award?.isWin) throw new BadRequestException(HAVE_ALREADLY_RECEIVED);

    const uniqueTaskIds = [
      ...new Set(
        campaign.Task?.filter((x) => x.taskTemplate.required === true)
          ?.map((x) => x.id)
          ?.flat()
      ),
    ];

    if (uniqueTaskIds.length) {
      const CheckComplete = await this.prisma.userTask.findMany({
        where: {
          userId,
          taskId: {
            in: uniqueTaskIds,
          },
        },
      });

      if (CheckComplete.length !== uniqueTaskIds.length) throw new BadRequestException(
        'Please complete all required campaign tasks'
      );
    }

    try {
      if (
        campaign.methodOfselectWinners == MethodOfselectWinners.AUTO_PRIZEE_DRAW
      ) {
        if (user.UserCampaign[0]?.award) {
          if (user.UserCampaign[0].award.campaignRewardId) {
            return {
              isWin: true,
              award: user.UserCampaign[0].award.campaignReward,
            };
          } else {
            return { isWin: false };
          }
        }
        // if (!campaign.settingForNotWin) {
        //   await this.prisma.userAward.create({
        //     data: {
        //       userCampaignId: user.UserCampaign[0].id,
        //       value: 0,
        //       isWin: 'false',
        //     },
        //   });

        //   return { isWin: false };
        // }

        // Number of gifts received
        let numberOfAwardsAwarded = 0;
        for (const item of campaign.UserClaimCampaign) {
          if (item?.award && item?.award?.isWin !== 'false') numberOfAwardsAwarded++;
        }

        // Check the number of remaining gifts
        const numberOfRemainingPrizes = campaign.numberOfPrizes - numberOfAwardsAwarded;

        // array containing the id of the gift list
        const prizesArr = [];

        for (const item of campaign.CampaignReward) {
          // Number of gifts received in that reward campaign
          const numberOfReceivedPrize = item.userAward?.filter(u => u.isWin !== 'false')?.length ?? 0;
          for (
            let index = 0;
            index < item.numberOfWinningTicket - numberOfReceivedPrize;
            index++
          ) {
            prizesArr.push(item.id);
          }
        }

        // Number of people who participated in receiving rewards
        const currentParticipants = campaign?.UserClaimCampaign?.length ?? 1;
        const totalParticipants = campaign.totalNumberOfUsersAllowedToWork;

        const rate = Math.random();

        const winRate = this.calculateWinningProbability(currentParticipants, totalParticipants, numberOfAwardsAwarded, campaign.numberOfPrizes)

        // random ra số nhỏ hơn tỷ lệ thì win
        if (winRate >= rate && prizesArr.length) {
          // Get a random gift
          const randomNumber = Math.floor(Math.random() * prizesArr.length)

          const reward = await this.prisma.campaignReward.findUnique({
            where: { id: prizesArr[randomNumber] },
          });

          await this.prisma.userAward.create({
            data: {
              userCampaignId: user.UserCampaign[0].id,
              campaignRewardId: reward.id,
              value: reward.amountOfMoney,
              isFinalPrize: numberOfRemainingPrizes == 1 ? true : false,
              isWin: 'true',
            },
          });

          if(campaign?.settingForNotWin && numberOfRemainingPrizes == 1) {
            await this.prisma.campaign.update({
              where: { id: user.UserCampaign[0].campaignId },
              data: {
                status: CampaignStatus.COMPLETION,
                endReason: CampaignEndReason.WINNER_LIMIT_REACHED
              },
            });
          }

          return { isWin: true, award: reward };
        } else {
          await this.prisma.userAward.create({
            data: {
              userCampaignId: user.UserCampaign[0].id,
              value: 0,
              isWin: 'false',
            },
          });

          return { isWin: false };
        }
      } else {
        if (!user.UserCampaign[0].award) {
          await this.prisma.userAward.create({
            data: {
              userCampaignId: user.UserCampaign[0].id,
              value: 0,
              isWin: 'waiting',
            },
          });
        }

        return { isWin: true };
      }
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      console.log("user gacha: ", error)
      throw new BadRequestException(error?.message);
    }
  }

  calculateWinningProbability(currentParticipants: number, totalParticipants: number, giftsGiven: number, totalGifts: number) {
    const winRate = totalGifts / totalParticipants
    let probability = winRate
    const totalParticipants1 = Math.floor(totalParticipants * 0.25)
    const totalParticipants2 = Math.floor(totalParticipants * 0.4)
    const totalParticipants3 = Math.floor(totalParticipants * 0.5)
    const totalParticipants4 = Math.floor(totalParticipants * 0.85)
    const totalParticipants5 = totalParticipants
    const totalGifts1 = Math.floor(totalGifts * 0.25)
    const totalGifts2 = Math.floor(totalGifts * 0.4)
    const totalGifts3 = Math.floor(totalGifts * 0.5)
    const totalGifts4 = Math.floor(totalGifts * 0.85)
    const totalGifts5 = totalGifts

    // hết quà
    if (giftsGiven === totalGifts) {
      return 0
    }

    // level 1: currentParticipants  0 - 25%
    if (currentParticipants <= totalParticipants1) {
      const giftsAtLevel = totalGifts1 - giftsGiven

      if (giftsAtLevel && totalParticipants1 - currentParticipants + 1 <= giftsAtLevel) {
        probability = 1
      }
      // level 2: currentParticipants  25 - 40%: win rate 15%
    } else if (currentParticipants <= totalParticipants2) {
      const giftsAtLevel = totalGifts2 - giftsGiven
      if (giftsAtLevel && totalParticipants2 - currentParticipants + 1 <= giftsAtLevel) {
        probability = 1
      }
      // level 3: currentParticipants  40 - 50%: win rate 10%
    } else if (currentParticipants <= totalParticipants3) {
      const giftsAtLevel = totalGifts3 - giftsGiven
      if (giftsAtLevel && totalParticipants3 - currentParticipants + 1 <= giftsAtLevel) {
        probability = 1
      }
      // level 4: currentParticipants  50 - 85%: win rate 35%
    } else if (currentParticipants <= totalParticipants4) {
      const giftsAtLevel = totalGifts4 - giftsGiven
      if (giftsAtLevel && totalParticipants4 - currentParticipants + 1 <= giftsAtLevel) {
        probability = 1
      }
      // level 5: currentParticipants  85 - 100%: win rate 15%
    } else {
      const giftsAtLevel = totalGifts5 - giftsGiven
      if (giftsAtLevel && totalParticipants5 - currentParticipants + 1 <= giftsAtLevel) {
        probability = 1;
      }
    }
    console.log("currentParticipants: ", currentParticipants, "totalParticipants: ", totalParticipants, "giftsGiven: ", giftsGiven, "totalGifts: ", totalGifts, "probability: ", probability)

    return probability
  }

}
