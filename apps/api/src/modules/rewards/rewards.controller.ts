import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { RewardsService } from './rewards.service';
import { CurrentUser } from '../../helpers/current-user-decorator';
import { AccessTokenParsed } from '../auth/auth.interface';
import {
  CreateRewardDto,
  GetRewardListParamsDto,
  UpdateRewardDto,
} from './rewards.dto';
import { ApiTags } from '@nestjs/swagger';
import { Public } from '../auth/public.decorator';

@Controller('rewards')
@ApiTags('rewardsAPI')
export class RewardsController {
  constructor(private readonly rewardsService: RewardsService) {}

  @Public()
  @Get()
  async list(@Query() query: GetRewardListParamsDto) {
    return this.rewardsService.list(query);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: AccessTokenParsed,
    @Body('campaignId') campaignId: string,
    @Body('data') createRewardDto: CreateRewardDto[]
  ) {
    return this.rewardsService.create(user.id, campaignId, createRewardDto);
  }

  @Put()
  async update(
    @CurrentUser() user: AccessTokenParsed,
    @Body('campaignId') campaignId: string,
    @Body('data') updateRewardDto: UpdateRewardDto[]
  ) {
    return this.rewardsService.update(user.id, campaignId, updateRewardDto);
  }

  @Delete()
  async delete(
    @CurrentUser() user: AccessTokenParsed,
    @Body('campaignId') campaignId: string,
    @Body('rewardIds') rewardIds: number[]
  ) {
    return this.rewardsService.delete(user.id, campaignId, rewardIds);
  }

  @Post('users/:userId')
  async userGacha(
    @CurrentUser() user: AccessTokenParsed,
    @Body('campaignId') campaignId: string
  ) {
    return this.rewardsService.userGacha(user.id, campaignId);
  }
}
