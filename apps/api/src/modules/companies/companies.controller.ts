import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { CompaniesService } from './companies.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { CurrentUser } from '../../helpers/current-user-decorator';
import { AccessTokenParsed } from '../auth/auth.interface';
import { CreateCompanyDto, GetCompanyListParamsDto } from './companies.dto';
import { ApiTags } from '@nestjs/swagger';
import { Membership } from '@prisma/client';
import { ListRequest } from '../../utils/requests.common';
import { Public } from '../auth/public.decorator';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { diskStorage } from 'multer';
import { NovuApiKey } from '../../helpers/current-NovuApiKey';

@Controller('companies')
@ApiTags('companiesAPI')
export class CompaniesController {
  constructor(private readonly companiesService: CompaniesService) {}

  @Post()
  @UseInterceptors(FileInterceptor('companyImage'))
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: AccessTokenParsed,
    @NovuApiKey() novuApiKey: string,
    @Body() createCompanyDto: CreateCompanyDto,
    @UploadedFile() companyImage: Express.Multer.File
  ) {
    return await this.companiesService.create(
      user.id,
      {
        ...createCompanyDto,
        companyImage,
      },
      novuApiKey
    );
  }

  @Get()
  async getAllCompany(
    @CurrentUser() user: AccessTokenParsed,
    @Query() query: GetCompanyListParamsDto
  ) {
    return this.companiesService.getAll(user.id, query);
  }

  @UseInterceptors(FileInterceptor('companyImage'))
  @Put(':companyId')
  async updateCompany(
    @CurrentUser() user: AccessTokenParsed,
    @Param('companyId', ParseIntPipe) companyId: number,
    @Body() updateCompanyDto: CreateCompanyDto,
    @UploadedFile() companyImage: Express.Multer.File
  ) {
    return await this.companiesService.updateCompany(user.id, companyId, {
      ...updateCompanyDto,
      companyImage,
    });
  }

  @Get(':companyId/users')
  async listUserInCompany(
    @CurrentUser() user: AccessTokenParsed,
    @Param('companyId', ParseIntPipe) companyId: number,
    @Query() query: ListRequest
  ) {
    return this.companiesService.listUsers(user.id, companyId, query);
  }

  @Get(':companyId')
  async getDetails(
    @CurrentUser() user: AccessTokenParsed,
    @Param('companyId', ParseIntPipe) companyId: number
  ) {
    return this.companiesService.getDetails(user.id, companyId);
  }

  @Post(':companyId/users')
  async addUserInCompany(
    @CurrentUser() user: AccessTokenParsed,
    @Param('companyId', ParseIntPipe) companyId: number,
    @Body('email') email: string,
    @Body('membership') membership: Membership
  ) {
    return this.companiesService.addUserInCompany(
      user?.id,
      companyId,
      email,
      membership
    );
  }

  @Put(':companyId/users/:userId')
  async updateUserInCompany(
    @CurrentUser() user: AccessTokenParsed,
    @Param('companyId', ParseIntPipe) companyId: number,
    @Param('userId', ParseIntPipe) userId: number,
    @Body('membership') membership: Membership,
    @Body('isTest') isTest: boolean
  ) {
    return this.companiesService.updateUserInCompany(
      user?.id,
      userId,
      companyId,
      membership,
      isTest
    );
  }

  @Delete(':companyId/users/:userId')
  async removeUserInCompany(
    @CurrentUser() user: AccessTokenParsed,
    @Param('companyId', ParseIntPipe) companyId: number,
    @Param('userId', ParseIntPipe) userId: number
  ) {
    return this.companiesService.removeUserInCompany(
      user?.id,
      userId,
      companyId
    );
  }

  @Delete(':companyId/')
  async delete(@Param('companyId', ParseIntPipe) companyId: number) {
    return this.companiesService.delete(+companyId);
  }
}
