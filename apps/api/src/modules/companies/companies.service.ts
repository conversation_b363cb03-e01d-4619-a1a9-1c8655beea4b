import {
  COMPANY_CODE_CONFLICT,
  COMPANY_NOT_FOUND,
  EMAIL_CONFLICT,
  IMAGE_TOO_LARGE,
  NEED_AT_LEAST_ONE_MANAGER,
  UNAUTHORIZED_RESOURCE,
  USER_HAVE_COMPANY,
  USER_NOT_FOUND,
} from '@clout/common/constants/errors.constants';
import { PrismaService } from '@clout/prisma/prisma.service';
import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CreateCompanyDto, GetCompanyListParamsDto } from './companies.dto';
import { Membership, Prisma } from '@prisma/client';
import { SquareService } from '@clout/common/providers/square/square.service';
import { ListRequest } from '../../utils/requests.common';
import { randomUUID } from 'crypto';
import { NovuService } from '@clout/common/providers/novu/novu.service';
import { Configuration } from '@clout/common/config/configuration.interface';
import { S3Service } from '@clout/common/providers/s3/s3.service';
import { CampaignsService } from '../campaigns/campaigns.service';
import { Novu } from '@novu/node';
// import { Cron } from '@nestjs/schedule';

@Injectable()
export class CompaniesService {
  logger = new Logger(CompaniesService.name);
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private squareService: SquareService,
    private novuService: NovuService,
    private s3Service: S3Service,
    private campaignsService: CampaignsService
  ) { }

  async create(
    userId: number,
    createCompanyDto: CreateCompanyDto,
    apiKey: string
  ) {
    const { companyImage, email, sourceId, ...createData } = createCompanyDto;
    this.logger.log(
      `{
        userId: ${userId},
        email: ${email},
        sourceId: ${sourceId},
        createData: ${JSON.stringify(createData)}
      }`
    );

    if(companyImage.size >= 2 * 1024 * 1024) throw new BadRequestException(IMAGE_TOO_LARGE)

    const user = await this.prisma.user.findUnique({ where: { id: userId } });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    if (user.companyId) throw new ForbiddenException(UNAUTHORIZED_RESOURCE);

    const emailSafe = email.toLowerCase();
    const checkEmail = await this.prisma.email.findFirst({
      where: { email: emailSafe },
    });

    if (checkEmail && checkEmail?.companyId)
      throw new ConflictException(EMAIL_CONFLICT);

    const checkCode = await this.prisma.company.findFirst({
      where: { code: createData.code },
    });

    if (checkCode) throw new ConflictException(COMPANY_CODE_CONFLICT);

    try {
      const imageUrl = await this.transformMulterImageData(
        userId,
        companyImage
      );

      if (sourceId) {
        const squareCreateCustomer = await this.squareService.createCustomer(
          createData.code
        );
        if (!squareCreateCustomer.status)
          throw new BadRequestException('create square customer error');
        const squareCreateCard = await this.squareService.createCard(
          sourceId,
          squareCreateCustomer.customerId
        );
        if (!squareCreateCard.status)
          throw new BadRequestException('create square card error');

        createData['squareCustomerId'] = squareCreateCustomer.customerId;
        createData['squareCardId'] = squareCreateCard.cardId;
      }

      if (createData.cardInfo && typeof createData.cardInfo == 'string') {
        createData.cardInfo = JSON.parse(createData.cardInfo);
      }
      const novuId = randomUUID();
      let newCompany = await this.prisma.company.create({
        data: {
          ...createData,
          novuId,
          image: {
            create: {
              imageUrl,
            },
          },
          companyRole: {
            create: {
              userId,
              membership: Membership.MANAGER,
            },
          },
          member: {
            connect: {
              id: user.id,
            },
          },
        },
      });

      if (newCompany) {
        let emailIdAdd = null;
        if (checkEmail) {
          await this.prisma.email.update({
            where: { id: checkEmail.id },
            data: {
              companyId: newCompany.id,
            },
          });
          emailIdAdd = checkEmail.id;
        } else {
          const newEmail = await this.prisma.email.create({
            data: {
              email: emailSafe,
              companyId: newCompany.id,
            },
          });
          emailIdAdd = newEmail.id;
        }
        newCompany = await this.prisma.company.update({
          where: { id: newCompany.id },
          data: { emailId: emailIdAdd },
        });

        const novu = new Novu(apiKey);

        await novu.subscribers.identify(novuId, {
          email: emailSafe,
        });
      }

      return { company: newCompany };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async updateCompany(
    userId: number,
    companyId: number,
    updateCompanyDto: CreateCompanyDto
  ) {
    this.logger.log(
      `{
        userId: ${userId},
        companyId: ${companyId},
        updateCompanyDto: ${JSON.stringify(updateCompanyDto)},
      }`
    );
    let emailUpdate = null;
    let checkEmail = null;
    const updateData = {};
    const { email, name, companyImage, sourceId, cardInfo } = updateCompanyDto;


    if(companyImage.size >= 2 * 1024 * 1024) throw new BadRequestException(IMAGE_TOO_LARGE)

    const user = await this.prisma.user.findUnique({ where: { id: userId } });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const company = await this.prisma.company.findUnique({
      where: {
        id: companyId,
      },
      include: { email: true },
    });

    if (!company) throw new NotFoundException(COMPANY_NOT_FOUND);

    if (email) {
      emailUpdate = updateCompanyDto.email.toLowerCase();
      checkEmail = await this.prisma.email.findFirst({
        where: {
          email: emailUpdate,
        },
      });

      if (checkEmail && checkEmail?.companyId)
        throw new ConflictException(EMAIL_CONFLICT);
    }

    try {
      if (sourceId) {
        const squareDisableCard = await this.squareService.disableCard(
          company.squareCardId
        );

        if (!squareDisableCard.status)
          throw new BadRequestException('disable square card error');

        const squareCreateCard = await this.squareService.createCard(
          sourceId,
          company.squareCustomerId
        );

        if (!squareCreateCard.status)
          throw new BadRequestException('create square card error');
        updateData['squareCardId'] = squareCreateCard.cardId;
      }
      if (cardInfo) {
        updateData['cardInfo'] =
          typeof cardInfo == 'string' ? JSON.parse(cardInfo) : cardInfo;
      }
      let companyUpdate = await this.prisma.company.update({
        where: { id: company.id },
        data: {
          ...updateData,
          ...(name ? { name } : {}),
          ...(companyImage
            ? {
              image: {
                update: {
                  imageUrl: await this.transformMulterImageData(
                    userId,
                    companyImage
                  ),
                },
              },
            }
            : {}),
        },
        include: {
          email: true,
          image: true,
        },
      });
      let emailIdUpdate = 0;
      if (companyUpdate && emailUpdate) {
        if (company.email?.userId && checkEmail) {
          if (company.email?.id) {
            await this.prisma.email.update({
              where: { id: company.email?.id },
              data: { companyId: null },
            });
          }
          await this.prisma.email.update({
            where: { id: checkEmail.id },
            data: {
              companyId: companyUpdate.id,
            },
          });
          emailIdUpdate = checkEmail.id;
        } else if (company.email?.userId && !checkEmail) {
          if (company.email?.id) {
            await this.prisma.email.update({
              where: { id: company.email?.id },
              data: { companyId: null },
            });
          }
          const newEmail = await this.prisma.email.create({
            data: {
              email: emailUpdate,
              companyId: companyUpdate.id,
            },
          });
          emailIdUpdate = newEmail.id;
        } else if (!company.email?.userId && checkEmail) {
          if (company.email?.id) {
            await this.prisma.email.delete({
              where: { id: company.email?.id },
            });
          }
          await this.prisma.email.update({
            where: { id: checkEmail.id },
            data: {
              companyId: companyUpdate.id,
            },
          });
          emailIdUpdate = checkEmail.id;
        } else if (!company.email?.userId && !checkEmail) {
          if (company.email?.id) {
            await this.prisma.email.update({
              where: { id: company.email?.id },
              data: {
                email: emailUpdate,
              },
            });
          } else {
            const newEmail = await this.prisma.email.create({
              data: {
                email: emailUpdate,
                companyId: company.id,
              },
            });
            emailIdUpdate = newEmail.id;
          }
        }
        if (emailIdUpdate !== 0) {
          companyUpdate = await this.prisma.company.update({
            where: { id: companyUpdate.id },
            data: {
              emailId: emailIdUpdate,
            },
            include: {
              email: true,
              image: true,
            },
          });
        }
      }
      return { company: companyUpdate };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async listUsers(userId: number, companyId: number, query: ListRequest) {
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    const company = await this.prisma.company.findUnique({
      where: { id: companyId },
    });
    if (!company) throw new NotFoundException(COMPANY_NOT_FOUND);
    if (user?.companyId != companyId)
      throw new ForbiddenException(UNAUTHORIZED_RESOURCE);
    try {
      let users = [];
      let total = 0;
      if (user && user.companyId) {
        const { skip, take, orderBy } = query;
        [users, total] = await this.prisma.$transaction([
          this.prisma.user.findMany({
            skip,
            take,
            where: {
              companyId: user.companyId,
            },
            include: {
              companyRole: true,
              email: true,
            },
            ...(orderBy
              ? { orderBy }
              : {
                orderBy: {
                  createdAt: 'desc',
                },
              }),
          }),
          this.prisma.user.count({
            where: {
              companyId: user.companyId,
            },
          }),
        ]);
      }
      return { users, total };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async getDetails(userId: number, companyId: number) {
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    const company = await this.prisma.company.findUnique({
      where: { id: companyId },
    });
    if (!company) throw new NotFoundException(COMPANY_NOT_FOUND);
    return company;
  }

  async getAll(userId: number, query: GetCompanyListParamsDto) {
    try {
      const user = await this.prisma.user.findFirst({
        where: {
          id: userId
        }
      })

      if (!user || !user.isSuperAdmin) {
        throw new ForbiddenException('Forbidden')
      }

      const { q } = query
      const whereClause: Prisma.CompanyWhereInput = {
        OR: [
          {
            name: {
              contains: q,
              mode: 'insensitive',
            }
          },
          {
            email: {
              email: {
                contains: q,
                mode: 'insensitive',
              }
            }
          },
          {
            code: {
              contains: q,
              mode: 'insensitive',
            }
          },
          {
            id: {
              equals: +q || 0,
            }
          }
        ]
      }

      const company = await this.prisma.company.findMany({
        where: whereClause,
      })

      return company;
    } catch (error) {
      console.log(error)
      throw new BadRequestException(error)
    }
  }

  async addUserInCompany(
    userId: number,
    companyId: number,
    email: string,
    membership: Membership
  ) {
    const user = await this.prisma.user.findUnique({ where: { id: userId } });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const company = await this.prisma.company.findUnique({
      where: { id: companyId },
    });

    if (!company) throw new NotFoundException(COMPANY_NOT_FOUND);

    if (user?.companyId != companyId)
      throw new ForbiddenException(UNAUTHORIZED_RESOURCE);

    const emailSafe = email.toLowerCase();
    const checkNewUser = await this.prisma.user.findFirst({
      where: { email: { email: emailSafe } },
    });

    if (!checkNewUser) throw new NotFoundException(USER_NOT_FOUND);

    if (checkNewUser.companyId) throw new ConflictException(USER_HAVE_COMPANY);
    await this.prisma.user.update({
      where: { id: checkNewUser.id },
      data: {
        companyId,
        companyRole: {
          create: {
            companyId,
            membership,
          },
        },
      },
    });
    return { status: true, message: 'Success' };
  }

  async updateUserInCompany(
    currentId: number,
    userId: number,
    companyId: number,
    membership: Membership,
    isTest?: boolean
  ) {
    if (!isTest) {
      const company = await this.prisma.company.findUnique({
        where: { id: companyId },
      });

      if (!company) throw new NotFoundException(COMPANY_NOT_FOUND);

      const current = await this.prisma.user.findUnique({
        where: { id: currentId },
        include: { companyRole: true },
      });

      if (
        !current ||
        current?.companyRole?.membership != Membership.MANAGER ||
        current?.companyId != companyId
      )
        throw new ForbiddenException(UNAUTHORIZED_RESOURCE);

      const user = await this.prisma.user.findUnique({ where: { id: userId } });

      if (!user) throw new NotFoundException(USER_NOT_FOUND);

      if (user?.companyId != companyId)
        throw new ForbiddenException(UNAUTHORIZED_RESOURCE);

      if (membership != Membership.MANAGER) {
        const checkNumberMemberIsManager = await this.prisma.companyRole.count({
          where: {
            companyId,
            userId: {
              not: user.id,
            },
            membership: Membership.MANAGER,
          },
        });

        if (checkNumberMemberIsManager == 0)
          throw new ForbiddenException(NEED_AT_LEAST_ONE_MANAGER);
      }
    }
    const userUpdate = await this.prisma.companyRole.update({
      where: {
        userId_companyId: {
          userId,
          companyId,
        },
      },
      data: {
        membership,
        isVerified: true,
      },
      include: {
        user: true,
      },
    });

    return { user: userUpdate };
  }

  async removeUserInCompany(
    currentId: number,
    userId: number,
    companyId: number
  ) {
    const company = await this.prisma.company.findUnique({
      where: { id: companyId },
    });
    if (!company) throw new NotFoundException(COMPANY_NOT_FOUND);
    const current = await this.prisma.user.findUnique({
      where: { id: currentId },
      include: { companyRole: true },
    });
    if (
      !current ||
      current?.companyRole?.membership != Membership.MANAGER ||
      current?.companyId != companyId
    )
      throw new ForbiddenException(UNAUTHORIZED_RESOURCE);
    const user = await this.prisma.user.findUnique({ where: { id: userId } });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    if (user?.companyId != companyId)
      throw new ForbiddenException(UNAUTHORIZED_RESOURCE);
    const checkNumberMemberIsManager = await this.prisma.companyRole.count({
      where: {
        companyId,
        userId: {
          not: user.id,
        },
        membership: Membership.MANAGER,
      },
    });
    if (checkNumberMemberIsManager == 0)
      throw new ForbiddenException(NEED_AT_LEAST_ONE_MANAGER);
    const userDelete = await this.prisma.user.update({
      where: { id: user.id },
      data: {
        companyId: null,
        companyRole: {
          delete: {
            companyId,
          },
        },
      },
    });
    return { user: userDelete };
  }

  async delete(companyId: number) {
    const company = await this.prisma.company.findUnique({
      where: { id: companyId },
      include: { email: true, campaign: true },
    });
    if (!company) throw new NotFoundException(COMPANY_NOT_FOUND);
    try {
      await this.prisma.companyRole.deleteMany({ where: { companyId } });

      if (company.email) {
        if (company.email?.userId) {
          await this.prisma.email.update({
            where: {
              id: company.emailId,
            },
            data: {
              companyId: null,
            },
          });
        } else {
          await this.prisma.email.delete({
            where: { id: company.emailId },
          });
        }
      }

      if (company.campaign.length) {
        for (const campaign of company.campaign) {
          await this.campaignsService.delete(campaign.id, true);
        }
      }
      if (company.imageId) {
        await this.prisma.image.delete({ where: { id: company.imageId } });
      }

      await this.prisma.company.delete({ where: { id: companyId } });

      return company;
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error);
    }
  }

  // private transformMulterImageData(image: Express.Multer.File) {
  //   const baseUrl = this.configService.get<string>('meta.appUrl');
  //   return `${baseUrl}/${image.path}`;
  // }

  private async transformMulterImageData(
    userId: number,
    image: Express.Multer.File
  ) {
    const date = new Date().toISOString().replace(/-|\.|:/g, '');
    const managementCode = `${userId}_${date}`;
    const randomName = Array(16)
      .fill(null)
      .map(() => Math.round(Math.random() * 16).toString(16))
      .join('');
    const bucketName =
      this.configService.get<Configuration['s3']['bucketName']>(
        's3.bucketName'
      );
    const uploadFileParams = {
      name: managementCode + '-' + randomName + '-' + image.originalname,
      body: image.buffer,
      bucket: bucketName,
    };
    const uploadedImage = await this.s3Service.upload(uploadFileParams);
    console.log({ uploadedImage });

    return uploadedImage?.Location;
  }
}
