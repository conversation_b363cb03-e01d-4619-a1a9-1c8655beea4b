import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { ListRequest } from '../../utils/requests.common';

export class CreateCompanyDto {
  @ApiPropertyOptional({ type: 'string', example: 'companyName' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ type: 'string', example: 'companyCode' })
  @IsString()
  @IsOptional()
  code?: string;

  @ApiPropertyOptional({ type: 'string', example: 'email' })
  @IsString()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({ type: 'string' })
  @IsOptional()
  companyImage?: Express.Multer.File;

  @ApiPropertyOptional({ type: 'string' })
  @IsOptional()
  cardInfo?: string;

  @ApiPropertyOptional({ type: 'string' })
  @IsString()
  @IsOptional()
  sourceId?: string;
}

export class GetCompanyListParamsDto extends ListRequest { }


