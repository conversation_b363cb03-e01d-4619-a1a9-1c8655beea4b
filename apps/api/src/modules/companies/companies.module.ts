import { Module } from '@nestjs/common';
import { CompaniesService } from './companies.service';
import { CompaniesController } from './companies.controller';
import { PrismaModule } from '@clout/prisma/prisma.module';
import { ConfigModule } from '@nestjs/config';
import { TokensModule } from '@clout/common/providers/tokens/tokens.module';
import { SquareModule } from '@clout/common/providers/square/square.module';
import { NovuModule } from '@clout/common/providers/novu/novu.module';
import { S3Module } from '@clout/common/providers/s3/s3.module';
import { CampaignsModule } from '../campaigns/campaigns.module';

@Module({
  imports: [
    PrismaModule,
    ConfigModule,
    TokensModule,
    NovuModule,
    SquareModule,
    S3Module,
    CampaignsModule,
  ],
  providers: [CompaniesService],
  controllers: [CompaniesController],
})
export class CompaniesModule {}
