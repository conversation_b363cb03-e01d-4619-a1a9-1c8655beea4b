/* eslint-disable @typescript-eslint/no-explicit-any */
import { Controller, Get } from '@nestjs/common';
import { DefaultService } from './default.service';
import { Public } from '../auth/public.decorator';
import { ApiTags } from '@nestjs/swagger';
@Controller()
@Public()
@ApiTags('defaultAPI')
export class DefaultController {
  constructor(private readonly defaultService: DefaultService) {}

  @Public()
  @Get('master-data/groups')
  async getAllMasterDataByGroups(): Promise<any> {
    return this.defaultService.getAllMasterDataByGroups();
  }
}
