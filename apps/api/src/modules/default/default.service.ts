import { PrismaService } from '@clout/prisma/prisma.service';
import { Injectable } from '@nestjs/common';
import _ from 'lodash';

@Injectable()
export class DefaultService {
  constructor(private prisma: PrismaService) {}

  async getAllMasterDataByGroups() {
    const data = await this.prisma.systemMetadata.findMany({
      orderBy: { index: 'asc' },
    });
    return _.chain(data).groupBy('group').value();
  }
}
