import { IdentityType, MfaMethod, User } from '.prisma/client';
import {
  TWITTER_USER_CONFLICT,
  USER_NOT_FOUND,
} from '@clout/common/constants/errors.constants';
import { TokensService } from '@clout/common/providers/tokens/tokens.service';
import { PrismaService } from '@clout/prisma/prisma.service';
import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createDigest, createRandomBytes } from '@otplib/plugin-crypto';
import { keyDecoder, keyEncoder } from '@otplib/plugin-thirty-two';
import axios from 'axios';
import { authenticator } from 'otplib';
import {
  AccessTokenClaims,
  MfaTokenPayload,
  TokenResponse,
  TotpTokenResponse,
} from '../auth/auth.interface';
import {
  LOGIN_ACCESS_TOKEN,
  MULTI_FACTOR_TOKEN,
} from '@clout/common/providers/tokens/tokens.constants';
import client from 'twilio';
import { RegisterTwitterDto } from './accounts.dto';
import UAParser from 'ua-parser-js';
import { GeolocationService } from '@clout/common/providers/geolocation/geolocation.service';

const TWITTER_OAUTH_CLIENT_ID = process.env.TWITTER_OAUTH_CLIENT_ID;
// const TWITTER_OAUTH_CLIENT_SECRET = process.env.TWITTER_OAUTH_CLIENT_SECRET;
const TWITTER_URL = 'https://api.twitter.com/2';
// const BasicAuthToken = Buffer.from(
//   `${TWITTER_OAUTH_CLIENT_ID}:${TWITTER_OAUTH_CLIENT_SECRET}`,
//   'utf8'
// ).toString('base64');

const twitterOauthTokenParams = {
  client_id: TWITTER_OAUTH_CLIENT_ID,
  // based on code_challenge
  code_verifier: 'challenge',
  grant_type: 'authorization_code',
};

// the shape of the object we should recieve from twitter in the request
type TwitterTokenResponse = {
  token_type: 'bearer';
  expires_in: 7200;
  access_token: string;
  scope: string;
  refresh_token: string;
};

type TwitterUser = {
  id: string;
  name: string;
  username: string;
};

@Injectable()
export class AccountService {
  authenticator: typeof authenticator;
  logger = new Logger(AccountService.name);
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private tokensService: TokensService,
    private geolocationService: GeolocationService
  ) {
    this.authenticator = authenticator.create({
      keyEncoder,
      keyDecoder,
      createDigest,
      createRandomBytes,
      step: 600, //seconds
      digits: 4,
    });
  }

  // the main step 1 function, getting the access token from twitter using the code that twitter sent us
  async getTwitterOAuthToken(code: string, redirect_uri: string) {
    try {
      // POST request to the token url to get the access token
      const res = await axios.post<TwitterTokenResponse>(
        'https://api.twitter.com/2/oauth2/token',
        new URLSearchParams({ ...twitterOauthTokenParams, redirect_uri, code }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            Authorization: `Basic ${process.env.TWITTER_TOKEN}`,
          },
        }
      );
      return res.data;
    } catch (err) {
      console.error(err);

      return null;
    }
  }
  // getting the twitter user from access token

  async getTwitterUser(accessToken: string) {
    try {
      // request GET https://api.twitter.com/2/users/me
      const res = await axios.get<{ data: TwitterUser }>(
        `${TWITTER_URL}/users/me`,
        {
          headers: {
            'Content-type': 'application/json',
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      return res.data.data ?? null;
    } catch (err) {
      console.error(err);

      return null;
    }
  }

  async twitterOauth(
    code: string,
    state: 'SIGNIN' | 'CONNECT',
    redirect_uri: string,
    ip: string,
    userAgent: string,
    userId?: number
  ) {
    console.log({
      code,
      state,
      redirect_uri,
    });

    const TwitterOAuthToken = await this.getTwitterOAuthToken(
      code,
      redirect_uri
    );
    if (!TwitterOAuthToken) {
      return { status: false, message: 'code error' };
    }
    const twitterUser = await this.getTwitterUser(
      TwitterOAuthToken.access_token
    );
    if (!twitterUser) {
      return { status: false, message: 'user not found' };
    }
    if (state == 'SIGNIN') {
      return await this.loginTwitter(
        {
          twitterId: twitterUser.id,
          name: twitterUser.name,
          accountName: twitterUser.username,
          refreshToken: TwitterOAuthToken.refresh_token,
        },
        ip,
        userAgent
      );
    } else if (state == 'CONNECT') {
      return await this.connectTwitter(userId, {
        twitterId: twitterUser.id,
        name: twitterUser.name,
        accountName: twitterUser.username,
        refreshToken: TwitterOAuthToken.refresh_token,
      });
    }
    return { twitterUser };
  }

  async deleteAccountTwitter(userId: number, twitterId: number) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    const identity = await this.prisma.identity.update({
      where: { id: twitterId, userId: user.id },
      data: {
        deletedAt: new Date(),
        active: false,
      },
    });
    return identity;
  }



  async connectTwitter(userId: number, _data: RegisterTwitterDto) {
    const { twitterId, name, accountName, refreshToken } = _data;
    const checkTwitterAccount = await this.prisma.identity.findFirst({
      where: {
        accountId: twitterId,
        type: IdentityType.TWITTER,
        active: true,
        deletedAt: null,
      },
      include: {
        user: true,
      },
    });
    // 2. if the linked identity exist, throw error
    if (checkTwitterAccount) throw new ConflictException(TWITTER_USER_CONFLICT);
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        name,
        identities: {
          create: {
            accountId: twitterId,
            accountName,
            type: IdentityType.TWITTER,
            active: true,
            token: refreshToken,
          },
        },
      },
    });
    return { status: true, message: 'Success' };
  }

  async loginTwitter(
    _data: RegisterTwitterDto,
    ipAddress: string,
    userAgent: string
  ): Promise<TokenResponse | TotpTokenResponse | object> {
    let user = null;
    const { twitterId, name, accountName, refreshToken } = _data;
    const linkedIdentity = await this.prisma.identity.findFirst({
      where: {
        accountId: twitterId,
        type: IdentityType.TWITTER,
        active: true,
        deletedAt: null,
      },
      include: {
        user: {
          include: {
            email: true,
            identities: {
              where: {
                active: true,
                deletedAt: null,
              },
            },
            companyRole: true,
            memberCompany: {
              include: {
                email: true,
                image: true,
              },
            },
          },
        },
      },
    });
    if (!linkedIdentity) {
      const id = await this.generateUserId();
      const createdIdentity = await this.prisma.identity.create({
        data: {
          accountId: twitterId,
          accountName,
          type: IdentityType.TWITTER,
          active: true,
          token: refreshToken,
          user: {
            create: {
              id,
              name,
              twoFactorSecret: this.authenticator.generateSecret(),
              isVerified: true,
              twoFactorMethod: MfaMethod.NONE,
            },
          },
        },
        include: {
          user: {
            include: {
              email: true,
              identities: {
                where: {
                  active: true,
                  deletedAt: null,
                },
              },
              companyRole: true,
              memberCompany: {
                include: {
                  email: true,
                  image: true,
                },
              },
            },
          },
        },
      });
      user = createdIdentity.user;
    } else {
      user = linkedIdentity.user;
    }
    user['havePassword'] = false;
    if (user?.password) user['havePassword'] = true;
    if (user.twoFactorMethod == MfaMethod.TOTP) {
      const { totpToken } = await this.sendSMS({
        sendBy: 'MESSAGE',
        phoneNumber: user.twoFactorPhone,
        userId: user.id,
      });
      return {
        totpToken,
        user,
      };
    } else {
      return this.loginResponse(ipAddress, userAgent, user);
    }
  }

  async sendSMS(data: {
    sendBy: 'MESSAGE' | 'CALL';
    phoneNumber?: string;
    userId: number;
  }) {
    const { sendBy, phoneNumber, userId } = data;
    console.log({ sendBy, phoneNumber, userId });
    const accountSid = this.configService.get<string>('TWILIO_ACCOUNT_SID');
    const authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN');
    const phoneFrom = this.configService.get<string>('TWILIO_PHONE_NUMBER');
    console.log({ accountSid, authToken, phoneFrom });
    const clientAuth = client(accountSid, authToken);
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    try {
      const code = this.getOneTimePassword(user.twoFactorSecret);
      console.log({ code });
      const phoneNumberFromVN = [
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
        '**********',
      ];
      const checkPhoneNumberFromVN = phoneNumber
        ? phoneNumberFromVN.includes(phoneNumber)
        : phoneNumberFromVN.includes(user.twoFactorPhone);
      let phoneNumberContact = '';
      if (checkPhoneNumberFromVN) {
        phoneNumberContact = phoneNumber
          ? `+84${phoneNumber.slice(1)}`
          : `+84${user.twoFactorPhone.slice(1)}`;
      } else {
        phoneNumberContact = phoneNumber
          ? `+81${phoneNumber.slice(1)}`
          : `+81${user.twoFactorPhone.slice(1)}`;
      }
      console.log({ phoneNumberContact });
      const mfaTokenPayload: MfaTokenPayload = {
        type: MfaMethod.TOTP,
        id: userId,
      };
      const totpToken = this.tokensService.signJwt(
        MULTI_FACTOR_TOKEN,
        mfaTokenPayload,
        '10m'
      );
      try {
        if (sendBy == 'CALL') {
          const convertedCode = code.split('').join('. ');
          clientAuth.calls
            .create({
              twiml: `
              <Response>
                <Say language="ja-JP">
                  <prosody rate="-25%">[clout]認証コードは次の通りです：${convertedCode}. s\n有効期限は10分です。</prosody>
                </Say>
              </Response>
            `,
              to: phoneNumberContact,
              from: phoneFrom,
            })
            .then((call) => console.log(call.sid))
            .catch((error) => {
              console.log('error: ', { error });
            });
        } else {
          clientAuth.messages
            .create({
              body: `[clout]認証コードは次の通りです：${code}\n有効期限は10分です。`,
              to: phoneNumberContact,
              from: phoneFrom,
            })
            .then((call) => console.log(call.sid))
            .catch((error) => {
              console.log('error: ', { error });
            });
        }
      } catch (error) {
        console.log('error', { error });
      }
      return { status: true, message: 'Success', totpToken };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error);
    }
  }

  private getOneTimePassword(secret: string): string {
    return this.authenticator.generate(secret);
  }

  private async generateUserId() {
    let id: number | undefined = undefined;
    while (!id) {
      id = Number(
        `10${await this.tokensService.generateRandomString(6, 'numeric')}`
      );
      const users = await this.prisma.user.findMany({ where: { id }, take: 1 });
      if (users.length) id = undefined;
    }
    return id;
  }

  private async loginResponse(
    ipAddress: string,
    userAgent: string,
    user: User
  ): Promise<object> {
    try {
      const token = await this.tokensService.generateRandomString(64);
      const ua = new UAParser(userAgent);
      const location = await this.geolocationService.getLocation(ipAddress);
      const operatingSystem =
        `${ua.getOS().name ?? ''} ${ua.getOS().version ?? ''}`
          .replace('Mac OS', 'macOS')
          .trim() || undefined;
      const existingMobileSessions = await this.prisma.session.findMany({
        where: {
          userId: user.id,
          OR: [
            {
              userAgent: { startsWith: 'okhttp' }, // for android
            },
            {
              userAgent: { startsWith: 'Matching' }, // for ios
            },
          ],
        },
        orderBy: {
          updatedAt: 'asc',
        },
      });
      const MAX_SESSION = 1;
      if (existingMobileSessions.length >= MAX_SESSION) {
        for (let i = 0; i < MAX_SESSION - 1; i++) {
          existingMobileSessions.pop();
        }
        await this.prisma.session.deleteMany({
          where: {
            id: { in: existingMobileSessions.map((e) => e.id) },
          },
        });
      }
      const { id } = await this.prisma.session.create({
        data: {
          token,
          ipAddress,
          city: location?.city?.names?.en,
          region: location?.subdivisions?.pop()?.names?.en,
          timezone: location?.location?.time_zone,
          countryCode: location?.country?.iso_code,
          userAgent,
          browser:
            `${ua.getBrowser().name ?? ''} ${
              ua.getBrowser().version ?? ''
            }`.trim() || undefined,
          operatingSystem,
          user: { connect: { id: user.id } },
        },
      });
      const accessToken = await this.getAccessToken(user, id);
      return {
        accessToken,
        refreshToken: token,
        user: this.prisma.expose(user),
      };
    } catch (error) {
      console.log({ error });
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  private async getAccessToken(user: User, sessionId: number): Promise<string> {
    const payload: AccessTokenClaims = {
      id: user.id,
      sessionId,
      scopes: [],
    };
    return this.tokensService.signJwt(
      LOGIN_ACCESS_TOKEN,
      payload,
      this.configService.get<string>('security.accessTokenExpiry')
    );
  }
}
