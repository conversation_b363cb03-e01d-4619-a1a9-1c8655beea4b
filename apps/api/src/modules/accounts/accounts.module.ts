import { Module } from '@nestjs/common';
import { AccountService } from './accounts.service';
import { AccountController } from './accounts.controller';
import { PrismaModule } from '@clout/prisma/prisma.module';
import { TokensModule } from '@clout/common/providers/tokens/tokens.module';
import { ConfigModule } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { GeolocationModule } from '@clout/common/providers/geolocation/geolocation.module';
import { StaartStrategy } from '../auth/staart.strategy';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    GeolocationModule,
    PrismaModule,
    TokensModule,
    ConfigModule,
  ],
  providers: [AccountService, StaartStrategy],
  controllers: [AccountController],
  exports: [AccountService],
})
export class AccountModule {}
