import {
  Body,
  Controller,
  Ip,
  Post,
  Query,
  Headers,
  Delete,
  Param,
  ParseIntPipe,
  Get,
  UploadedFile,
} from '@nestjs/common';
import { AccountService } from './accounts.service';
import { AccessTokenParsed } from '../auth/auth.interface';
import { CurrentUser } from '../../helpers/current-user-decorator';
import { ApiTags } from '@nestjs/swagger';
import { Public } from '../auth/public.decorator';

@Controller('accounts')
@ApiTags('accountsAPI')
export class AccountController {
  constructor(private readonly accountService: AccountService) {}

  @Post()
  async redirectTwitter(
    @CurrentUser() user: AccessTokenParsed,
    @Query('token') token: string,
    @Body()
    data: {
      code: string;
      state: 'SIGNIN' | 'CONNECT';
      redirect_uri: string;
    },
    @Ip() ip: string,
    @Headers('User-Agent') userAgent: string
  ): Promise<object> {
    const { code, state, redirect_uri } = data;
    return this.accountService.twitterOauth(
      code,
      state,
      redirect_uri,
      ip,
      userAgent,
      user?.id !== 0 ? user.id : null
    );
  }

  @Delete(':accountId')
  async deleteAccountTwitter(
    @CurrentUser() user: AccessTokenParsed,
    @Param('accountId', ParseIntPipe) accountId: number
  ): Promise<object> {
    return this.accountService.deleteAccountTwitter(user?.id, accountId);
  }
}
