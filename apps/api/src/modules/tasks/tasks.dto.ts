import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TaskType } from '@prisma/client';
import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { ListRequest } from '../../utils/requests.common';

export class CreateTaskDto {
  @ApiPropertyOptional()
  @IsEnum(TaskType)
  @IsOptional()
  type?: TaskType;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  taskActionType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  taskTemplate?: {
    userName: string;
    link: string;
    config: object;
    points: number;
    required: boolean;
  };

  @ApiPropertyOptional({ default: false })
  @IsBoolean()
  @IsOptional()
  required?: boolean;

  @ApiProperty()
  @IsNumber()
  point: number;
}

export class UpdateTaskDto extends CreateTaskDto {
  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  taskId?: number;

  @ApiPropertyOptional()
  @IsOptional()
  taskTemplate?: {
    userName: string;
    link: string;
    config: object;
    points: number;
    required: boolean;
  };
}

export class GetTaskListParamsDto extends ListRequest {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  campaignId?: string;
}
