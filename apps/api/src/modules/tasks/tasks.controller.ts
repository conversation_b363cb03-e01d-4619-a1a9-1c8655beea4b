import {
  Controller,
  Post,
  Body,
  Put,
  Delete,
  Get,
  Query,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { TaskService } from './tasks.service';
import { CurrentUser } from '../../helpers/current-user-decorator';
import { AccessTokenParsed } from '../auth/auth.interface';
import { ApiTags } from '@nestjs/swagger';
import {
  CreateTaskDto,
  GetTaskListParamsDto,
  UpdateTaskDto,
} from './tasks.dto';
import { Public } from '../auth/public.decorator';

@Controller('tasks')
@ApiTags('tasksAPI')
export class TaskController {
  constructor(private readonly taskService: TaskService) {}

  @Get()
  async list(
    @CurrentUser() user: AccessTokenParsed,
    @Query() query: GetTaskListParamsDto
  ) {
    return this.taskService.list(query, user?.id !== 0 ? user?.id : null);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: AccessTokenParsed,
    @Body('campaignId') campaignId: string,
    @Body('data') createTaskDto: CreateTaskDto[]
  ) {
    return this.taskService.create(user.id, campaignId, createTaskDto);
  }

  @Put()
  async update(
    @CurrentUser() user: AccessTokenParsed,
    @Body('campaignId') campaignId: string,
    @Body('data') updateTaskDto: UpdateTaskDto[]
  ) {
    return this.taskService.update(user.id, campaignId, updateTaskDto);
  }

  @Delete()
  async delete(
    @CurrentUser() user: AccessTokenParsed,
    @Body('campaignId') campaignId: string,
    @Body('taskIds') taskIds: number[]
  ) {
    return this.taskService.delete(user.id, campaignId, taskIds);
  }
}
