import {
  CAMPAIGN_NOT_FOUND,
  COMPANY_NOT_FOUND,
  TASK_HAS_DONE,
  TASK_NOT_FOUND,
  UNAUTHORIZED_RESOURCE,
  USER_NOT_FOUND,
} from '@clout/common/constants/errors.constants';
import { PrismaService } from '@clout/prisma/prisma.service';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateTaskDto,
  GetTaskListParamsDto,
  UpdateTaskDto,
} from './tasks.dto';

@Injectable()
export class TaskService {
  logger = new Logger(TaskService.name);
  constructor(private prisma: PrismaService) {}

  async list(query: GetTaskListParamsDto, userId?: number) {
    console.log({ userId });

    let tasks = [];
    let total = 0;
    const { skip, take, orderBy, campaignId } = query;
    if (campaignId) {
      const campaign = await this.prisma.campaign.findUnique({
        where: {
          id: campaignId,
        },
      });

      if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);
    }

    if (userId) {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) throw new NotFoundException(USER_NOT_FOUND);
    }

    try {
      [tasks, total] = await this.prisma.$transaction([
        this.prisma.task.findMany({
          ...(skip ? { skip } : {}),
          ...(take ? { take } : {}),
          where: { campaignId },
          include: {
            taskTemplate: true,
            ...(userId
              ? {
                  UserTask: {
                    where: {
                      userId,
                    },
                  },
                }
              : {}),
          },
          ...(orderBy
            ? { orderBy }
            : {
                orderBy: {
                  id: 'asc',
                },
              }),
        }),
        this.prisma.task.count({ where: { campaignId } }),
      ]);
      return { tasks, total };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async create(
    userId: number,
    campaignId: string,
    createTaskDto: CreateTaskDto[]
  ) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { companyRole: true },
    });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    if ((!user.companyId || !user?.companyRole?.isVerified) && !user.isSuperAdmin)
      throw new NotFoundException(COMPANY_NOT_FOUND);

    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
      include: {
        Task: {
          include: {
            taskTemplate: true,
          },
        },
      },
    });

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);

    const checkRequiredCampaign = campaign.Task?.find(
      (task) => task?.taskTemplate?.required === true
    );

    const checkShareURLCampaign = campaign.Task?.find(
      (task) => task.type === 'SHARE_URL'
    );

    const checkRequiredDto = createTaskDto.find(
      (item) => item.taskTemplate?.required === true
    );
    if (!checkRequiredDto && !checkRequiredCampaign)
      throw new BadRequestException(
        'Campaign needs at least one required task'
      );

    try {
      return await this.prisma.$transaction(async (prisma) => {
        const newTasksData = [];
        for (const task of createTaskDto) {
          if (task.type === 'SHARE_URL' && checkShareURLCampaign) {
            const taskUpdate = await this.prisma.task.update({
              where: {
                id: checkShareURLCampaign.id,
              },
              data: {
                campaignId: campaign.id,
                type: task.type,
                taskActionType: task.taskActionType,
                taskTemplateId: checkShareURLCampaign.taskTemplateId,
              },
            });

            await this.prisma.taskTemplate.update({
              where: {
                id: checkShareURLCampaign.id,
              },
              data: {
                config: task.taskTemplate.config,
                points: task.taskTemplate.points,
                link: task.taskTemplate.link,
                userName: task.taskTemplate.userName,
                required: task.taskTemplate.required,
              },
            });

            newTasksData.push(taskUpdate);
          } else {
            const taskTemplate = await prisma.taskTemplate.create({
              data: {
                config: task.taskTemplate.config,
                points: task.taskTemplate.points,
                link: task.taskTemplate.link,
                userName: task.taskTemplate.userName,
                required: task.taskTemplate.required,
              },
            });

            const newTask = await prisma.task.create({
              data: {
                campaignId: campaign.id,
                type: task.type,
                taskActionType: task.taskActionType,
                taskTemplateId: taskTemplate.id,
              },
              include: {
                taskTemplate: true,
              },
            });

            newTasksData.push(newTask);
          }
        }

        return newTasksData;
      });
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error);
    }
  }

  async update(
    userId: number,
    campaignId: string,
    updateTaskDto: UpdateTaskDto[]
  ) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { companyRole: true },
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    // if (!user.companyId || !user?.companyRole?.isVerified)
    //   throw new NotFoundException(COMPANY_NOT_FOUND);
    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);
    if (campaign?.companyId !== user?.companyId && !user.isSuperAdmin)
      throw new ForbiddenException(UNAUTHORIZED_RESOURCE);
    try {
      return await this.prisma.$transaction(async (tx) => {
        const newTasksData = [];
        for (const task of updateTaskDto) {
          const { taskId, taskTemplate, ...updateData } = task;
          const taskCheck = await tx.task.findUnique({
            where: { id: taskId },
          });
          if (!taskCheck) throw new NotFoundException(TASK_NOT_FOUND);
          const updateTask = await tx.task.update({
            where: { id: taskId },
            data: {
              campaignId: campaign.id,
              ...updateData,
            },
            include: {
              taskTemplate: true,
            },
          });
          await tx.taskTemplate.update({
            where: { id: updateTask.taskTemplateId },
            data: {
              config: task?.taskTemplate.config,
              points: task?.taskTemplate.points,
              link: task?.taskTemplate.link,
              userName: task?.taskTemplate.userName,
              required: task?.taskTemplate.required,
            },
          });
          newTasksData.push(updateTask);
        }

        const tasks = await tx.task.findMany({
          where: {
            campaignId,
          },
          include: {
            taskTemplate: true,
          },
        });

        if (!tasks.find((item) => item?.taskTemplate?.required === true))
          throw new BadRequestException(
            'Campaign needs at least one required task'
          );

        return newTasksData;
      });
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async delete(userId: number, campaignId: string, taskIds: number[]) {
    if (!taskIds.length) {
      return { status: true, message: 'Success' };
    }
    taskIds = taskIds.map((taskId) => +taskId);
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { companyRole: true },
    });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
      include: {
        Task: {
          include: {
            taskTemplate: true,
          },
        },
      },
    });

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);

    if (
      campaign?.companyId !== user?.companyId &&
      (!user.companyId || !user?.companyRole?.isVerified) && !user.isSuperAdmin
    )
      throw new ForbiddenException(UNAUTHORIZED_RESOURCE);

    try {
      await this.prisma.$transaction(async (tx) => {
        await tx.userTask.deleteMany({ where: { taskId: { in: taskIds } } });
        await tx.task.deleteMany({ where: { id: { in: taskIds } } });
        await tx.taskTemplate.deleteMany({
          where: {
            id: {
              in: campaign.Task.filter((t) =>
                taskIds.find((id) => t.id == +id)
              ).map((item) => Number(item.taskTemplateId)),
            },
          },
        });

        const tasks = await tx.task.findMany({
          where: {
            campaignId,
          },
          include: {
            taskTemplate: true,
          },
        });

        if (!tasks.some((item) => item?.taskTemplate?.required === true)) {
          console.log('Campaign needs at least one required task');
          throw new BadRequestException(
            'Campaign needs at least one required task'
          );
        }
      });

      return { status: true, message: 'Success' };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      console.log('Campaign err:', error);
      throw new BadRequestException('Campaign err:', error);
    }
  }
}
