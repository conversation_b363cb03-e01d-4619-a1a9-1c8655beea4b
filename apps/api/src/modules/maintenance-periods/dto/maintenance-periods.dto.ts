import {
  ApiProperty,
  ApiPropertyOptional,
  ApiResponseProperty,
  PartialType,
} from '@nestjs/swagger';
import { StatusMaintenance } from '@prisma/client';
import { ListRequest } from 'apps/api/src/utils/requests.common';
import { Transform } from 'class-transformer';
import {
  IsDate,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

enum ApiStatus {
  SUCCESS = 'success',
  FAILED = 'failed',
}

export class ListMaintenancePeriodResponseDto {
  @ApiResponseProperty({ enum: ApiStatus })
  status: ApiStatus;

  @ApiResponseProperty()
  data?: object;

  @ApiResponseProperty()
  message?: string;
}

export class CreateMaintenancePeriodDto {
  @ApiProperty()
  @IsDate()
  @Transform((e) => new Date(e.value))
  startAt: Date;

  @ApiProperty()
  @IsDate()
  @Transform((e) => new Date(e.value))
  endAt: Date;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    enum: StatusMaintenance,
  })
  @IsOptional()
  status?: StatusMaintenance;
}

export class UpdateMaintenancePeriodDto extends PartialType(
  CreateMaintenancePeriodDto
) {}

export class GetMPListParamsDto extends ListRequest {}
