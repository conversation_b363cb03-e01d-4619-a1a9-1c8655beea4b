/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { MaintenancePeriodService } from './maintenance-periods.service';
import {
  CreateMaintenancePeriodDto,
  GetMPListParamsDto,
  ListMaintenancePeriodResponseDto,
  UpdateMaintenancePeriodDto,
} from './dto/maintenance-periods.dto';
import { CurrentUser } from '../../helpers/current-user-decorator';
import { AccessTokenParsed } from '../auth/auth.interface';
import { Public } from '../auth/public.decorator';

@Controller('maintenance-periods')
@ApiTags('MaintenancePeriods')
export class MaintenancePeriodController {
  constructor(
    private readonly maintenancePeriodService: MaintenancePeriodService
  ) {}

  @ApiOkResponse({ type: ListMaintenancePeriodResponseDto })
  @Get('')
  @Public()
  async getAll(@Query() query: GetMPListParamsDto): Promise<any> {
    return this.maintenancePeriodService.getAll(query);
  }

  @ApiOkResponse({ type: Object })
  @Post('')
  async create(
    @Body() input: CreateMaintenancePeriodDto,
    @CurrentUser() user: AccessTokenParsed
  ): Promise<any> {
    const checkAdmin = await this.maintenancePeriodService.checkAdmin(user?.id);
    if (!checkAdmin) {
      throw new ForbiddenException('Forbidden');
    }

    return this.maintenancePeriodService.create(input);
  }

  @ApiOkResponse({ type: Object })
  @Get(':id')
  async getById(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: AccessTokenParsed
  ): Promise<any> {
    const checkAdmin = await this.maintenancePeriodService.checkAdmin(user?.id);
    if (!checkAdmin) {
      throw new ForbiddenException('Forbidden');
    }

    return this.maintenancePeriodService.getById(id);
  }

  @ApiOkResponse({ type: Object })
  @Put(':id')
  async getAllMasterDataByGroups(
    @Param('id', ParseIntPipe) id: number,
    @Body() input: UpdateMaintenancePeriodDto,
    @CurrentUser() user: AccessTokenParsed
  ): Promise<any> {
    const checkAdmin = await this.maintenancePeriodService.checkAdmin(user?.id);
    if (!checkAdmin) {
      throw new ForbiddenException('Forbidden');
    }

    return this.maintenancePeriodService.update(id, input);
  }

  @ApiOkResponse({ type: Object })
  @Delete(':id')
  async delete(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: AccessTokenParsed
  ): Promise<any> {
    const checkAdmin = await this.maintenancePeriodService.checkAdmin(user?.id);
    if (!checkAdmin) {
      throw new ForbiddenException('Forbidden');
    }

    return this.maintenancePeriodService.delete(id);
  }
}
