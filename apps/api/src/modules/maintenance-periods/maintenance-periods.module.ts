import { Module } from '@nestjs/common';

import { PrismaModule } from '@clout/prisma/prisma.module';
import { MaintenancePeriodService } from './maintenance-periods.service';
import { MaintenancePeriodController } from './maintenance-periods.controller';
@Module({
  imports: [PrismaModule],
  providers: [MaintenancePeriodService],
  controllers: [MaintenancePeriodController],
  exports: [MaintenancePeriodService],
})
export class MaintenancePeriodModule {}
