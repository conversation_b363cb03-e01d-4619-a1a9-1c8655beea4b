import { PrismaService } from '@clout/prisma/prisma.service';
import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import {
  CreateMaintenancePeriodDto,
  GetMPListParamsDto,
  UpdateMaintenancePeriodDto,
} from './dto/maintenance-periods.dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class MaintenancePeriodService {
  constructor(private prisma: PrismaService) {}

  async getAll(query: GetMPListParamsDto) {
    const { skip, take, orderBy } = query;
    const whereClause: Prisma.MaintenancePeriodWhereInput = {
      deleteAt: null,
    };

    const data = await this.prisma.maintenancePeriod.findMany({
      where: whereClause,
      ...(skip ? { skip } : {}),
      ...(take ? { take } : {}),
      ...(orderBy
        ? { orderBy }
        : {
            orderBy: {
              id: 'asc',
            },
          }),
    });
    const total = await this.prisma.maintenancePeriod.count({
      where: whereClause,
    });

    return {
      data: {
        data: data,
        total: total,
      },
      status: 'success',
    };
  }

  async getById(id: number) {
    return this.prisma.maintenancePeriod.findFirst({
      where: { id },
    });
  }

  async create(input: CreateMaintenancePeriodDto) {
    const startAt = new Date(input.startAt);
    const endAt = new Date(input.endAt);
    const checkMP = await this.prisma.maintenancePeriod.findFirst({
      where: {
        OR: [
          {
            startAt: { lte: startAt },
            endAt: { gte: startAt },
          },
          {
            startAt: { lte: endAt },
            endAt: { gte: endAt },
          },
          {
            startAt: { gte: startAt },
            endAt: { lte: endAt },
          },
        ],
        deleteAt: null,
      },
    });

    if (checkMP) {
      throw new ConflictException('Coincided with another maintenance period');
    }

    return this.prisma.maintenancePeriod.create({
      data: {
        ...input,
      },
    });
  }

  async update(id: number, input: UpdateMaintenancePeriodDto) {
    const checkMP = await this.prisma.maintenancePeriod.findFirst({
      where: {
        id,
        deleteAt: null,
      },
    });

    if (!checkMP) {
      throw new NotFoundException('Maintenance period not found');
    }

    return this.prisma.maintenancePeriod.update({
      where: { id },
      data: {
        ...input,
      },
    });
  }

  async delete(id: number) {
    const checkMP = await this.prisma.maintenancePeriod.findFirst({
      where: {
        id,
        deleteAt: null,
      },
    });

    if (!checkMP) {
      throw new NotFoundException('Maintenance period not found');
    }

    return this.prisma.maintenancePeriod.update({
      where: { id },
      data: {
        deleteAt: new Date(),
      },
    });
  }

  async checkAdmin(userId?: number) {
    const user = await this.prisma.user.findFirst({
      where: {
        id: userId ?? 0,
        isAdmin: true,
      },
    });

    return user ? true : false;
  }
}
