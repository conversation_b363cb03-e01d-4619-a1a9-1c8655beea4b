import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';
import { ListRequest } from '../../utils/requests.common';

export class CreatePaymentDto {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  campaignId?: string;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  price?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  priceWithTax?: number;

  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  pointUse?: number;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isTest?: boolean;
}

export class GetPaymentListParamsDto extends ListRequest {
  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  id?: number;
}
