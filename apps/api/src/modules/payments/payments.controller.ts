import {
  Body,
  Controller,
  HttpStatus,
  Post,
  HttpCode,
  Get,
  Query,
} from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { ApiTags } from '@nestjs/swagger';
import { CreatePaymentDto, GetPaymentListParamsDto } from './payments.dto';
import { AccessTokenParsed } from '../auth/auth.interface';
import { CurrentUser } from '../../helpers/current-user-decorator';
import { ListRequest } from '../../utils/requests.common';
import { Public } from '../auth/public.decorator';

@Controller('payments')
@ApiTags('paymentsAPI')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: AccessTokenParsed,
    @Body() createPaymentDto: CreatePaymentDto
  ) {
    return await this.paymentsService.create(user.id, createPaymentDto);
  }

  @Get()
  async list(
    @CurrentUser() user: AccessTokenParsed,
    @Query() query: GetPaymentListParamsDto
  ) {
    return await this.paymentsService.list(user.id, query);
  }

  @Public()
  @Get('updateAfterTransaction')
  async test() {
    return await this.paymentsService.updateAfterTransaction();
  }
}
