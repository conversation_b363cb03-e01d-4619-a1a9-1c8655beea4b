import { PrismaService } from '@clout/prisma/prisma.service';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CreatePaymentDto, GetPaymentListParamsDto } from './payments.dto';
import {
  CAMPAIGN_HAVE_BEEN_PAID,
  COMPANY_NOT_FOUND,
  UNAUTHORIZED_RESOURCE,
  USER_NOT_FOUND,
  NOT_ENOUGH_POINTS,
  CAMPAIGN_NOT_FOUND,
  EXCEEDS_POINTS,
} from '@clout/common/constants/errors.constants';
import { Membership } from '.prisma/client';
import { SquareService } from '@clout/common/providers/square/square.service';
import { CampaignStatus, MethodOfselectWinners, PaymentType, Prisma, RewardType } from '@prisma/client';
import { ListRequest } from '../../utils/requests.common';
import { GET_USER } from '../../shares/prisma/user.select';

@Injectable()
export class PaymentsService {
  logger = new Logger(PaymentsService.name);
  constructor(
    private prisma: PrismaService,
    private squareService: SquareService
  ) { }

  async create(userId: number, createPaymentDto: CreatePaymentDto) {
    const { campaignId, price, priceWithTax, pointUse, isTest } =
      createPaymentDto;
    // this.logger.log(
    //   `{
    //     userId: ${userId},
    //     campaignId: ${campaignId},
    //     price: ${price},
    //     priceWithTax: ${priceWithTax},
    //     pointUse: ${pointUse},
    //   }`
    // );
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        memberCompany: true,
        companyRole: true,
      },
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
      include: {
        company: true,
      }
    });

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);

    if (campaign.status != CampaignStatus.DRAFT)
      throw new BadRequestException(CAMPAIGN_HAVE_BEEN_PAID);

    if (!user.memberCompany) throw new NotFoundException(COMPANY_NOT_FOUND);

    if (
      !user.memberCompany ||
      user.companyRole?.membership !== Membership.MANAGER
    )
      throw new ForbiddenException(UNAUTHORIZED_RESOURCE);

    const { id, squareCardId, squareCustomerId, pointTotal } =
      user.memberCompany;

    console.log({
      id,
      squareCardId,
      squareCustomerId,
      pointTotal,
      campaignId,
      price,
      priceWithTax,
      pointUse,
    });

    if (pointUse > price) throw new BadRequestException(EXCEEDS_POINTS)
    if (pointUse > pointTotal) throw new BadRequestException(NOT_ENOUGH_POINTS);

    try {
      let squarePayment = null;
      if (Math.round(pointUse) < Math.round(priceWithTax)) {
        if (!isTest) {
          squarePayment = await this.squareService.createPayment(
            squareCustomerId,
            squareCardId,
            Math.round(priceWithTax) - Math.round(pointUse)
          );
          console.log('squarePayment    ', {
            squarePayment: squarePayment?.error,
          });
        }
      }
      if (!squarePayment || squarePayment?.status) {
        let amountAfterTransaction = pointTotal;
        if (Math.round(pointUse) > 0) {
          amountAfterTransaction =
            pointTotal - Math.round(pointUse) > 0
              ? pointTotal - Math.round(pointUse)
              : 0;

          await this.prisma.company.update({
            where: { id },
            data: { pointTotal: amountAfterTransaction },
          });

          await this.prisma.transaction.create({
            data: {
              type: RewardType.POINT,
              beforeValue: pointTotal,
              afterValue: amountAfterTransaction,
            },
          });
        }
        await this.prisma.campaign.update({
          where: { id: campaign.id },
          data: {
            // status: CampaignStatus.UNDER_REVIEW,
            status: CampaignStatus.WAITING_FOR_PUBLICATION,
          },
        });
        const payment = await this.prisma.payment.create({
          data: {
            companyId: user.companyId,
            userId: user.id,
            // tổng số tiền quà từ campaign
            amount: price,
            ...(squarePayment
              ? {
                trace_id: squarePayment?.payment?.id,
                extra: squarePayment?.payment as object,
              }
              : {
                trace_id: 'NONE',
              }),
            campaignId: campaign.id,
            campaignName: campaign.title,
            // số điểm user sử dụng thay tiền phải thanh toán
            pointUse: Math.round(pointUse),
            amountAfterTransaction,
          },
        });
        return payment;
      } else {
        throw new BadRequestException('Payment error');
      }
    } catch (error) {
      // this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async list(userId: number, query: GetPaymentListParamsDto) {
    try {
      let payments = [];
      let total = 0;
      const { skip, take, orderBy, q } = query;

      const whereClause: Prisma.PaymentWhereInput = {
        company: {
          OR: [
            {
              name: {
                contains: q,
                mode: 'insensitive',
              }
            },
            {
              email: {
                email: {
                  contains: q,
                  mode: 'insensitive',
                }
              }
            },
            {
              code: {
                contains: q,
                mode: 'insensitive',
              }
            },
            {
              id: {
                equals: +q || 0,
              }
            }
          ]
        }
      }

      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });
      if (!user) throw new NotFoundException(USER_NOT_FOUND);
      if (!user.companyId && !user.isSuperAdmin) throw new NotFoundException(COMPANY_NOT_FOUND);
      [payments, total] = await this.prisma.$transaction([
        this.prisma.payment.findMany({
          ...(skip ? { skip } : {}),
          ...(take ? { take } : {}),
          where: {
            ...(!user.isSuperAdmin ? { companyId: user.companyId } : whereClause),
          },
          ...(orderBy
            ? { orderBy }
            : {
              orderBy: {
                createdAt: 'desc',
              },
            }),
            include: {
              company: true,
            }
        }),
        this.prisma.payment.count({
          where: {
            ...(!user.isSuperAdmin ? { companyId: user.companyId } : whereClause),
          },
        }),
      ]);
      return { payments, total };
    } catch (error) {
      // this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async calculatePayment(userId: number, campaignId: string) {
    const user = await this.prisma.user.findFirst({
      where: { id: userId },
      select: GET_USER,
    })
    const campaign = await this.prisma.campaign.findFirst({
      include: {
        image: true,
        CampaignReward: {
          include: { userAward: true },
        },
      },
      where: {
        id: campaignId,
        status: CampaignStatus.COMPLETION,
        methodOfselectWinners: MethodOfselectWinners.AUTO_PRIZEE_DRAW,
        companyId: user.companyId
      }
    })

    if (campaign && user) {
      let amountUse = 0;
      for (const reward of campaign.CampaignReward) {
        amountUse =
          amountUse + reward.amountOfMoney * reward.userAward.length;
      }

      const pointTotal = user.memberCompany.pointTotal +
        campaign.totalPrizeValue -
        amountUse

      await this.prisma.company.update({
        where: { id: user.companyId },
        data: {
          pointTotal
        },
      });

      return this.prisma.payment.create({
        data: {
          // tổng quà user đã nhận
          amount: amountUse,
          type: PaymentType.WITHDRAWAL,
          trace_id: 'NONE',
          amountAfterTransaction: pointTotal,
          userId: user.id,
          companyId: user.companyId,
          campaignId: campaign.id,
          campaignName: campaign.title,
          // số tiền dư từ campaign (tổng quà campaign - số quà đã nhận)
          pointUse: campaign.totalPrizeValue - amountUse,
        },
      });
    }
  }

  async updateAfterTransaction() {
    try {
      const companies = await this.prisma.company.findMany({})

      for (const company of companies) {
        const payments = await this.prisma.payment.findMany({
          where: {
            companyId: company.id
          },
          orderBy: {
            createdAt: 'asc'
          }
        })

        // amountAfterTransaction cũ (amountAfterTransaction (payment trước đó)) => update pointUse
        let amountAfterTransactionBefore = 0
        // amountAfterTransaction mới => update amountAfterTransaction
        let amountAfterTransactionTmp = 0
        const listNewPayment = []

        for (const payment of payments) {
          if (payment.type === PaymentType.PAYMENT) {
            const pointUse = amountAfterTransactionBefore - payment.amountAfterTransaction
            await this.prisma.payment.update({
              where: {
                id: payment.id
              },
              data: {
                pointUse: pointUse,
                amountAfterTransaction: amountAfterTransactionTmp - pointUse
              }
            })

            const checkCampaign = await this.prisma.campaign.findFirst({
              where: {
                id: payment.campaignId
              },
              include: {
                image: true,
                CampaignReward: {
                  include: { userAward: true },
                },
                createdUser: {
                  select: GET_USER
                }
              },
            })

            if (checkCampaign && checkCampaign?.status === CampaignStatus.COMPLETION) {
              const checkPayment = await this.prisma.payment.findFirst({
                where: {
                  campaignId: payment.campaignId,
                  companyId: company.id,
                  type: PaymentType.WITHDRAWAL,
                }
              })

              if (!checkPayment && !listNewPayment.find(item => item?.campaignId === payment.campaignId)) {
                listNewPayment.push({
                  id: payment.id,
                  campaignId: payment.campaignId
                })
                // pointTotal = amount + payment.amountAfterTransaction - amountUse
              }
            }

            amountAfterTransactionBefore = payment.amountAfterTransaction
            amountAfterTransactionTmp -= pointUse
          } else {
            const checkCampaign = await this.prisma.campaign.findFirst({
              where: {
                id: payment.campaignId
              },
              include: {
                image: true,
                CampaignReward: {
                  include: { userAward: true },
                },
                createdUser: {
                  select: GET_USER
                }
              },
            })

            let amount = 0;
            let amountUse = 0;

            for (const reward of checkCampaign.CampaignReward) {
              amountUse = amountUse + reward.amountOfMoney * reward.userAward.length;
            }

            const checkDuplicatePayment = await this.prisma.payment.findMany({
              where: {
                companyId: company.id,
                campaignId: payment.campaignId,
                type: PaymentType.PAYMENT,
              }
            })

            if (checkDuplicatePayment.length) {
              amount = checkDuplicatePayment
                .map(item => item.amount)
                .reduce((total, item) => total + item, 0);
            }

            await this.prisma.payment.update({
              where: {
                id: payment.id
              },
              data: {
                // tổng quà user đã nhận
                amount: amountUse,
                amountAfterTransaction: amountAfterTransactionTmp + amount - amountUse,
                // số tiền dư từ campaign (tổng quà campaign - số quà đã nhận)
                pointUse: checkCampaign.totalPrizeValue - amountUse,
              }
            })

            amountAfterTransactionBefore = payment.amountAfterTransaction
            amountAfterTransactionTmp += amount - amountUse
          }
        }

        for (const paymentId of listNewPayment.map(item => item?.id)) {
          const payment = await this.prisma.payment.findFirst({
            where: {
              id: paymentId
            },
          })

          let amount = payment.amount
          let amountUse = 0;

          const checkCampaign = await this.prisma.campaign.findFirst({
            where: {
              id: payment.campaignId
            },
            include: {
              image: true,
              CampaignReward: {
                include: { userAward: true },
              },
              createdUser: {
                select: GET_USER
              }
            },
          })

          for (const reward of checkCampaign.CampaignReward) {
            amountUse =
              amountUse + reward.amountOfMoney * reward.userAward.length;
          }

          const checkDuplicatePayment = await this.prisma.payment.findMany({
            where: {
              companyId: company.id,
              campaignId: payment.campaignId,
              type: PaymentType.PAYMENT,
            }
          })

          if (checkDuplicatePayment.length) {
            amount = checkDuplicatePayment
              .map(item => item.amount)
              .reduce((total, item) => total + item, 0);
          }

          await this.prisma.payment.create({
            data: {
              // tổng quà user đã nhận
              amount: amountUse,
              type: PaymentType.WITHDRAWAL,
              trace_id: 'NONE',
              amountAfterTransaction: amountAfterTransactionTmp + amount - amountUse,
              userId: checkCampaign.createdUserId,
              companyId: company.id,
              campaignId: checkCampaign.id,
              campaignName: checkCampaign.title,
              // số tiền dư từ campaign (tổng quà campaign - số quà đã nhận)
              pointUse: checkCampaign.totalPrizeValue - amountUse,
              // createdAt: checkCampaign.updatedAt,
              // updatedAt: checkCampaign.updatedAt,
            },
          });

          amountAfterTransactionTmp += amount - amountUse
        }

        await this.prisma.company.update({
          where: { id: company.id },
          data: {
            pointTotal: amountAfterTransactionTmp
          },
        });
      }
    } catch (error) {
      console.log(error);
    }

  }
}
