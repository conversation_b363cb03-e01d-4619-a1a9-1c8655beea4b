import { Module } from '@nestjs/common';
import { PaymentsService } from './payments.service';
import { PaymentsController } from './payments.controller';
import { PrismaModule } from '@clout/prisma/prisma.module';
import { AmazonGiftCardModule } from '@clout/common/providers/amazon_gift_card/amazon_gift_card.module';
import { SquareModule } from '@clout/common/providers/square/square.module';

@Module({
  imports: [PrismaModule, AmazonGiftCardModule, SquareModule],
  providers: [PaymentsService],
  controllers: [PaymentsController],
  exports: [PaymentsService],
})
export class PaymentsModule {}
