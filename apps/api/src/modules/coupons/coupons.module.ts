import { Module } from '@nestjs/common';
import { CouponsService } from './coupons.service';
import { CouponsController } from './coupons.controller';
import { PrismaModule } from '@clout/prisma/prisma.module';
import { AmazonGiftCardModule } from '@clout/common/providers/amazon_gift_card/amazon_gift_card.module';
import { PaymentsModule } from '../payments/payments.module';

@Module({
  imports: [PrismaModule, AmazonGiftCardModule, PaymentsModule],
  providers: [CouponsService],
  controllers: [CouponsController],
})
export class CouponsModule {}
