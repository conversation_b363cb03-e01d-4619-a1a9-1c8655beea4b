import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Query,
} from '@nestjs/common';
import { CouponsService } from './coupons.service';
import { CurrentUser } from '../../helpers/current-user-decorator';
import { AccessTokenParsed } from '../auth/auth.interface';
import { ListRequest } from '../../utils/requests.common';
import { ApiTags } from '@nestjs/swagger';
import { CouponType } from '@prisma/client';

@Controller('coupons')
@ApiTags('couponsAPI')
export class CouponsController {
  constructor(private readonly couponsService: CouponsService) {}

  @Get()
  async list(
    @CurrentUser() user: AccessTokenParsed,
    @Query() query: ListRequest
  ) {
    return await this.couponsService.list(query, user?.id);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  async create(
    @CurrentUser() user: AccessTokenParsed,
    @Body('campaignId') campaignId: string,
    @Body('couponType') couponType: CouponType
  ) {
    return await this.couponsService.create(user?.id, campaignId, couponType);
  }
}
