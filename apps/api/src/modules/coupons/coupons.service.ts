import { PrismaService } from '@clout/prisma/prisma.service';
import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ListRequest } from '../../utils/requests.common';
import {
  AWARD_HAS_BEEN_GIVEN,
  CAMPAIGN_NOT_FOUND,
  NO_PRIZE_FOUND,
  USER_NOT_FOUND,
} from '@clout/common/constants/errors.constants';
import { AmazonGiftCardService } from '@clout/common/providers/amazon_gift_card/amazon_gift_card.service';
import { CampaignEndReason, CampaignStatus, CouponType, RewardType } from '@prisma/client';
import { PaymentsService } from '../payments/payments.service';

@Injectable()
export class CouponsService {
  logger = new Logger(CouponsService.name);
  constructor(
    private prisma: PrismaService,
    private amazonGiftCardService: AmazonGiftCardService,
    private paymentsService: PaymentsService
  ) {}

  async list(query: ListRequest, userId?: number) {
    try {
      let coupons = [];
      let total = 0;
      const { skip, take, orderBy } = query;
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
      });
      if (!user) throw new NotFoundException(USER_NOT_FOUND);
      [coupons, total] = await this.prisma.$transaction([
        this.prisma.coupon.findMany({
          ...(skip ? { skip } : {}),
          ...(take ? { take } : {}),
          where: {
            userAward: {
              userCampaign: {
                userId: user.id,
              },
            },
          },
          ...(orderBy
            ? { orderBy }
            : {
                orderBy: {
                  createdAt: 'desc',
                },
              }),
        }),
        this.prisma.coupon.count({
          where: {
            userAward: {
              userCampaign: {
                userId: user.id,
              },
            },
          },
        }),
      ]);
      return { coupons, total };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async create(
    userId: number,
    campaignId: string,
    couponType: CouponType,
    isTest?: boolean
  ) {
    this.logger.log(
      `{
        userId: ${userId},
        campaignId: ${campaignId},
        couponType: ${couponType},
      }`
    );

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        UserCampaign: {
          where: { campaignId },
          include: { award: { include: { coupon: true } }, campaign: true },
        },
      },
    });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const campaign = await this.prisma.campaign.findUnique({
      where: { id: campaignId },
    });

    if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);

    if (user.UserCampaign[0]?.award?.coupon)
      throw new BadRequestException(AWARD_HAS_BEEN_GIVEN);

    if (!user.UserCampaign[0]?.award)
      throw new BadRequestException(NO_PRIZE_FOUND);

    try {
      const coupon: object = isTest
        ? {
            gcClaimCode: 'Test0011',
          }
        : await this.amazonGiftCardService.createGiftCard({
            creationRequestId: this.amazonGiftCardService.createRequestId(
              userId.toString()
            ),
            amount: Number(user.UserCampaign[0]?.award?.value),
            currencyCode: 'JPY',
          });

      this.logger.log(
        `{
          coupon: ${JSON.stringify(coupon)},
        }`
      );

      if (
        campaign?.settingForNotWin &&
        user.UserCampaign[0].award.isFinalPrize
      ) {
        await this.prisma.campaign.update({
          where: { id: user.UserCampaign[0].campaignId },
          data: {
            status: CampaignStatus.COMPLETION,
            endReason: CampaignEndReason.WINNER_LIMIT_REACHED
          },
        });

        await this.paymentsService.calculatePayment(user.UserCampaign[0].campaign.createdUserId, user.UserCampaign[0].campaignId)
      }

      const result = await this.prisma.userAward.update({
        where: { id: user.UserCampaign[0].award.id },
        data: {
          isWin: 'true',
          coupon: {
            create: {
              code: coupon['gcClaimCode'],
              type: couponType,
              transaction: {
                create: {
                  type: RewardType.AMAZON_GIFT,
                  extra: coupon,
                },
              },
            },
          },
        },
        include: {
          coupon: true,
        },
      });

      return result.coupon;
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }
}
