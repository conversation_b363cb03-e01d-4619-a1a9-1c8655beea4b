import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '@clout/prisma/prisma.service';
import {
  CAMPAIGN_NOT_FOUND,
  CODE_ERROR,
  COMPANY_NOT_FOUND,
  EMAIL_CONFLICT,
  INVALID_CREDENTIALS,
  PHONE_NUMBER_CONFLICT,
  TASK_HAS_DONE,
  TASK_NOT_FOUND,
  UNAUTHORIZED_RESOURCE,
  USER_NOT_FOUND,
} from '@clout/common/constants/errors.constants';
import { compare, hash } from 'bcrypt';
import { updateMe } from './me.dto';
import { IdentityType, Membership, Prisma, TaskType } from '@prisma/client';
import { NovuService } from '@clout/common/providers/novu/novu.service';
import { USER_REQUEST_COMPANY } from '@clout/common/providers/novu/novu.constant';
import { authenticator } from 'otplib';
import { keyDecoder, keyEncoder } from '@otplib/plugin-thirty-two';
import { createDigest, createRandomBytes } from '@otplib/plugin-crypto';
import sha256 from 'sha256';
import { Novu } from '@novu/node';
import { AccountService } from '../accounts/accounts.service';

@Injectable()
export class MeService {
  logger = new Logger(MeService.name);
  authenticator: typeof authenticator;
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private novuService: NovuService,
    private accountService: AccountService
  ) {
    this.authenticator = authenticator.create({
      keyEncoder,
      keyDecoder,
      createDigest,
      createRandomBytes,
      step: 600, //seconds
      digits: 4,
    });
  }

  async getProfile(userId: number): Promise<unknown> {
    // await this.accountService.checkConnectTwitter(userId);
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        email: true,
        identities: {
          where: {
            active: true,
            deletedAt: null,
          },
        },
        companyRole: true,
        memberCompany: {
          include: {
            email: true,
            image: true,
          },
        },
      },
    });
    if (!user) throw new UnauthorizedException(UNAUTHORIZED_RESOURCE);
    user['havePassword'] = false;
    user['isRequestMemberCompany'] = false;
    if (user?.password) user['havePassword'] = true;
    if (user?.companyId && !user?.companyRole?.isVerified) {
      user.companyId = null;
      user['isRequestMemberCompany'] = true;
      user.companyRole = null;
      user.memberCompany = null;
    }
    return user;
  }

  async updateMe(
    userId: number,
    data: updateMe,
    apiKey: string
  ): Promise<unknown> {
    const novu = new Novu(apiKey);
    const dataUpdate: Prisma.UserUpdateInput = {};

    let emailSafe = null;
    let checkMail = null;
    let userUpdated = null;
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        email: true,
      },
    });
    console.log({ user });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    if (data?.newPassword) {
      if (
        (!data?.password && user.password) ||
        (data?.password && !(await compare(data?.password, user?.password)))
      )
        throw new UnauthorizedException(INVALID_CREDENTIALS);
      const password = await hash(
        data.newPassword,
        this.configService.get<number>('security.saltRounds') ?? 10
      );
      dataUpdate.password = password;
    }
    if (data?.email) {
      emailSafe = data?.email.toLowerCase();
      const verify = user.isSuperAdmin && data?.code == '3002' ? true : this.authenticator.check(data?.code, sha256(emailSafe));

      if (!verify) throw new BadRequestException(CODE_ERROR);
      checkMail = await this.prisma.email.findFirst({
        where: {
          email: emailSafe,
        },
      });

      if (checkMail && checkMail?.userId)
        throw new ConflictException(EMAIL_CONFLICT);
    }
    if (data?.twoFactorPhone?.length > 0) {
      if(data.twoFactorPhone.length > 11 || data.twoFactorPhone.length < 10) throw new BadRequestException("twoFactorPhone must be longer than or equal to 10 characters")
      const checkPhoneNumber = await this.prisma.user.findFirst({
        where: {
          twoFactorPhone: data?.twoFactorPhone,
          id: {
            not: user.id,
          },
        },
      });
      if (checkPhoneNumber) throw new ConflictException(PHONE_NUMBER_CONFLICT);
    }
    delete data?.email;
    delete data?.password;
    delete data?.newPassword;
    delete data?.code;

    Object.keys(data).forEach((key) => {
      if (data[key] != null) {
        dataUpdate[key] = data[key];
      }
    });

    try {
      if (Object.keys(dataUpdate).length > 0) {
        userUpdated = await this.prisma.user.update({
          where: { id: user.id },
          data: dataUpdate,
        });
      }
      if (emailSafe) {
        let emailIdUpdate = 0;
        if (user.email?.companyId && checkMail) {
          await this.prisma.email.update({
            where: { id: user.emailId },
            data: { userId: null },
          });
          await this.prisma.email.update({
            where: { id: checkMail.id },
            data: {
              userId: user.id,
            },
          });
          emailIdUpdate = checkMail.id;
        } else if (user.email?.companyId && !checkMail) {
          await this.prisma.email.update({
            where: { id: user.emailId },
            data: { userId: null },
          });
          const newEmail = await this.prisma.email.create({
            data: {
              email: emailSafe,
              userId: user.id,
            },
          });
          emailIdUpdate = newEmail.id;
        } else if (!user.email?.companyId && checkMail) {
          if (user.emailId) {
            await this.prisma.email.delete({
              where: { id: user.emailId },
            });
          }
          await this.prisma.email.update({
            where: { id: checkMail.id },
            data: {
              userId: user.id,
            },
          });
          emailIdUpdate = checkMail.id;
        } else if (!user.email?.companyId && !checkMail) {
          if (user.emailId) {
            await this.prisma.email.update({
              where: { id: user.emailId },
              data: { email: emailSafe },
            });
          } else {
            const newEmail = await this.prisma.email.create({
              data: {
                email: emailSafe,
                userId: user.id,
              },
            });
            emailIdUpdate = newEmail.id;
          }
        }
        if (emailIdUpdate !== 0) {
          userUpdated = await this.prisma.user.update({
            where: { id: user.id },
            data: { emailId: emailIdUpdate },
          });
        }

        if (!user.emailId) {
          await novu.subscribers.identify(user.id.toString(), {
            email: emailSafe,
          });
        }
        if (user.emailId) {
          await novu.subscribers.update(user.id.toString(), {
            email: emailSafe,
          });
        }
      }

      return userUpdated || user;
    } catch (error) {
      console.log(error)
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async joinCompany(userId: number, companyCode: string, apiKey: string) {
    const checkCompany = await this.prisma.company.findFirst({
      where: { code: companyCode },
      include: { email: true },
    });

    if (!checkCompany) throw new NotFoundException(COMPANY_NOT_FOUND);

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: { email: true, companyRole: true },
    });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    try {
      if (!user.companyRole || user.companyRole.companyId !== checkCompany.id) {
        if (!user.companyRole) {
          await this.prisma.companyRole.create({
            data: {
              userId: user.id,
              companyId: checkCompany.id,
              membership: Membership.MEMBER,
              isVerified: false,
            },
          });
        } else {
          await this.prisma.companyRole.update({
            where: {
              userId: user.id,
            },
            data: {
              companyId: checkCompany.id,
              membership: Membership.MEMBER,
              isVerified: false,
            },
          });
        }

        await this.prisma.user.update({
          where: {
            id: user.id,
          },
          data: {
            companyId: checkCompany.id,
          },
        });

        const novu = new Novu(apiKey);

        await novu.trigger(USER_REQUEST_COMPANY, {
          to: {
            email: checkCompany.email.email,
            subscriberId: checkCompany.novuId,
          },
          payload: {
            link:
              this.configService.get<string>('frontendUrl') +
              '/campaign-creator/permission-management',
            linkShow:
              this.configService.get<string>('frontendUrl') +
              '/campaign-creator/permission-management',
          },
        });
      }

      return { statuss: true, message: 'Success' };
    } catch (error) {
      this.logger.error(JSON.stringify(error || {}));
      throw new BadRequestException(error?.message);
    }
  }

  async userCompleteTask(
    userId: number,
    userShareId: number,
    taskId: number,
    answer: string
  ) {
    let isOther = false;
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        UserTask: {
          where: {
            taskId,
          },
        },
        identities: {
          where: {
            active: true,
            type: IdentityType.TWITTER,
            deletedAt: null,
          },
        },
        email: true,
      },
    });
    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const task = await this.prisma.task.findUnique({
      where: { id: taskId },
      include: {
        campaign: {
          include: {
            Task: {
              where: {
                taskTemplate: {
                  required: true,
                },
              },
            },
          },
        },
      },
    });

    if (!task) throw new NotFoundException(TASK_NOT_FOUND);

    const taskShareUrl = await this.prisma.task.findFirst({
      where: {
        type: TaskType.SHARE_URL,
        campaignId: task.campaignId,
      },
    });

    if (!taskShareUrl || userId === userShareId) userShareId = null;

    if (userShareId) {
      const userShare = await this.prisma.user.findUnique({
        where: { id: userShareId },
      });
      if (!userShare) throw new NotFoundException(USER_NOT_FOUND);
    }

    if (user.UserTask.length > 0) throw new BadRequestException(TASK_HAS_DONE);
    try {
      await this.userVisitCampaign(userId, task.campaignId, userShareId);

      if (userShareId && userId !== userShareId) {
        isOther = true;
      }

      const userTask = await this.prisma.userTask.create({
        data: {
          userId: user.id,
          taskId: task.id,
          answer: answer ?? '',
          userShareId: userShareId ?? null,
        },
      });

      const countNumberUserTaskRequired = await this.prisma.userTask.count({
        where: {
          userId: user.id,
          task: {
            campaignId: task.campaignId,
            taskTemplate: {
              required: true,
            },
          },
        },
      });

      const checkUserCampaign = await this.prisma.userCampaign.findFirst({
        where: {
          userId: user.id,
          campaignId: task.campaignId,
        },
      });

      try {
        if (
          countNumberUserTaskRequired == task.campaign.Task.length &&
          !checkUserCampaign
        ) {
          await this.prisma.userCampaign.create({
            data: {
              userId: user.id,
              campaignId: task.campaignId,
              identityAccountName:
                user.identities?.[0]?.accountName ?? user.email?.email,
            },
          });

          if (isOther) {
            await this.prisma.userTask.create({
              data: {
                userId: userShareId,
                taskId: taskShareUrl.id,
                answer: answer ?? '',
              },
            });
          }
        }
      } catch (error) {
        const check = await this.prisma.userCampaign.findFirst({
          where: {
            userId: user.id,
            campaignId: task.campaignId,
          },
        });

        if (!check) throw new BadRequestException(error);
      }

      if (userShareId) await this.updatePointUser(userShareId);
      await this.updatePointUser(userId);

      return userTask;
    } catch (error) {
      console.log("user complete task: ", error)
      throw new BadRequestException(error);
    }
  }

  async fixUserCampaign(userId: number) {
    const user = await this.prisma.user.findFirst({
      where: {
        id: userId
      }
    })

    if (!user) throw new NotFoundException(USER_NOT_FOUND);
    if (user.isAdmin || user.isSuperAdmin) {
      const userCampaigns = await this.prisma.userCampaign.findMany({
        include: {
          award: true,
        },
        orderBy: {
          createdAt: 'asc'
        }
      });

      const campaignMap = new Map<string, any[]>();

      userCampaigns.forEach((campaign) => {
        const key = `${campaign.userId}-${campaign.campaignId}`;
        if (!campaignMap.has(key)) {
          campaignMap.set(key, []);
        }
        campaignMap.get(key).push(campaign);
      });

      const duplicates = Array.from(campaignMap.values()).filter(
        (campaigns) => campaigns.length > 1
      );

      const campaignsToDelete = [];
      for (const campaigns of duplicates) {
        let campaign = campaigns.find(item => item?.award)
        if (!campaign) campaign = campaigns[campaigns.length - 1]
        campaignsToDelete.push(campaigns.filter(item => item.id !== campaign.id));
      }

      await this.prisma.userCampaign.deleteMany({
        where: {
          id: {
            in: campaignsToDelete.flat().map(item => item.id)
          }
        }
      })

      return campaignsToDelete.flat().map(item => item.id)
    }
  }

  async userVisitCampaign(
    userId: number,
    campaignId: string,
    userShareId?: number
  ) {
    try {
      if (userShareId) {
        const userShare = await this.prisma.user.findUnique({
          where: { id: +userShareId },
        });
        if (!userShare) throw new NotFoundException(USER_NOT_FOUND);
      }

      const campaign = await this.prisma.campaign.findFirst({
        where: {
          id: campaignId,
        },
        include: {
          Task: true,
        },
      });

      if (!campaign) throw new NotFoundException(CAMPAIGN_NOT_FOUND);

      const checkVisit = await this.prisma.campaignView.findFirst({
        where: {
          userId,
          campaignId,
        },
      });

      if (checkVisit) {
        if (!checkVisit.userShareId && userShareId) {
          await this.prisma.campaignView.update({
            where: {
              id: checkVisit.id,
            },
            data: {
              userShareId: +userShareId,
            },
          });
        }

        return {
          status: true,
          message: 'You have already visited this campaign',
        };
      }

      await this.prisma.campaignView.create({
        data: {
          userId,
          campaignId,
          userShareId: +userShareId,
        },
      });

      return {
        status: true,
        message: 'Successfully accessed the campaign',
      };
    } catch (error) {
      console.log('visit: ', error);
    }
  }

  async updatePointUser(userId: number) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) throw new NotFoundException(USER_NOT_FOUND);

    const userTasks = await this.prisma.userTask.findMany({
      where: {
        userId: user.id,
      },
      include: {
        task: {
          include: {
            taskTemplate: true,
          },
        },
      },
    });

    const pointTotal = userTasks
      ?.map((item) => item.task?.taskTemplate?.points ?? 0)
      .reduce((acc, curr) => acc + +(curr ?? 0), 0);

    await this.prisma.user.update({
      where: {
        id: userId,
      },
      data: {
        pointTotal,
      },
    });
  }
}
