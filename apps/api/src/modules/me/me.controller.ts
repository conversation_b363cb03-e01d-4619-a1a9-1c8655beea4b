import {
  Body,
  Controller,
  Get,
  Put,
  Post,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { MeService } from './me.service';
import { AccessTokenParsed } from '../auth/auth.interface';
import { ApiTags } from '@nestjs/swagger';
import { CurrentUser } from '../../helpers/current-user-decorator';
import { updateMe } from './me.dto';
import { NovuApiKey } from '../../helpers/current-NovuApiKey';

@Controller('me')
@ApiTags('meAPI')
export class MeController {
  constructor(private readonly meService: MeService) {}

  @Get()
  async getProfile(@CurrentUser() user: AccessTokenParsed) {
    return this.meService.getProfile(user?.id);
  }

  @Put()
  async updateMe(
    @CurrentUser() user: AccessTokenParsed,
    @NovuApiKey() novuApiKey: string,
    @Body() data: updateMe
  ) {
    return this.meService.updateMe(user?.id, data, novuApiKey);
  }

  @Post('request/companies')
  async joinCompany(
    @CurrentUser() user: AccessTokenParsed,
    @Body('companyCode') companyCode: string,
    @NovuApiKey() novuApiKey: string
  ) {
    return this.meService.joinCompany(user?.id, companyCode, novuApiKey);
  }

  @Post('tasks/:taskId')
  async userCompleteTask(
    @CurrentUser() user: AccessTokenParsed,
    @Param('taskId', ParseIntPipe) taskId: number,
    @Body('answer') answer: string,
    @Body('userShareId') userShareId?: number
  ): Promise<object> {
    return this.meService.userCompleteTask(
      user?.id,
      userShareId,
      taskId,
      answer
    );
  }

  //user accesses shared tasks
  @Post('visit')
  async userVisitCampaign(
    @CurrentUser() user: AccessTokenParsed,
    @Body('campaignId') campaignId: string,
    @Body('userShareId') userShareId?: number
  ): Promise<object> {
    return this.meService.userVisitCampaign(user?.id, campaignId, userShareId);
  }


  @Get('fix-userCampaign')
  async fixUserCampaign(
    @CurrentUser() user: AccessTokenParsed,
  ) {
    return this.meService.fixUserCampaign(user?.id);
  }
}
