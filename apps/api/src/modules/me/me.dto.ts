import { ApiPropertyOptional } from '@nestjs/swagger';
import { <PERSON>faMethod } from '@prisma/client';
import {
  IsEnum,
  IsOptional,
  IsString,
  Length,
  MinLength,
} from 'class-validator';

export class updateMe {
  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @MinLength(8)
  password?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  @MinLength(8)
  newPassword?: string;

  @ApiPropertyOptional()
  @IsEnum(MfaMethod)
  @IsOptional()
  twoFactorMethod?: MfaMethod;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  // @Length(10, 11)
  twoFactorPhone?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  code?: string;
}
