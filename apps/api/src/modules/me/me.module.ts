import { Modu<PERSON> } from '@nestjs/common';
import { MeService } from './me.service';
import { MeController } from './me.controller';
import { PrismaModule } from '@clout/prisma/prisma.module';
import { ConfigModule } from '@nestjs/config';
import { NovuModule } from '@clout/common/providers/novu/novu.module';
import { AccountModule } from '../accounts/accounts.module';

@Module({
  imports: [PrismaModule, ConfigModule, NovuModule, AccountModule],
  controllers: [MeController],
  providers: [MeService],
})
export class MeModule {}
