import { Module } from '@nestjs/common';
import { BannersService } from './banners.service';
import { BannersController } from './banners.controller';
import { PrismaModule } from '@clout/prisma/prisma.module';
import { ConfigModule } from '@nestjs/config';
import { S3Module } from '@clout/common/providers/s3/s3.module';


@Module({
  imports: [PrismaModule, ConfigModule, S3Module,],
  providers: [BannersService],
  controllers: [BannersController],
})
export class BannersModule {}
