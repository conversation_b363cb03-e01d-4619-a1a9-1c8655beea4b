import {
  ApiProperty,
  ApiPropertyOptional,
  ApiResponseProperty,
} from '@nestjs/swagger';
import { BannerType } from '@prisma/client';
import {
  IsArray,
  IsBoolean,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { ListRequest } from '../../utils/requests.common';

export enum ApiStatus {
  SUCCESS = 'success',
  FAILED = 'failed',
}

export class BannerResponseDto {
  @ApiResponseProperty({ enum: ApiStatus })
  status: ApiStatus;

  @ApiResponseProperty()
  data?: object;

  @ApiResponseProperty()
  message?: string;
}
export class DeleteBannersResponseDto {
  @ApiResponseProperty({ enum: ApiStatus })
  status: ApiStatus;

  @ApiResponseProperty()
  data?: object;

  @ApiResponseProperty()
  message?: string;
}

export class BannersResponseDto {
  @ApiResponseProperty({ enum: ApiStatus })
  status: ApiStatus;

  @ApiResponseProperty()
  data?: object[];

  @ApiResponseProperty()
  message?: string;
}

export class ImageDto {
  @ApiPropertyOptional()
  @IsNumber()
  @IsOptional()
  id?: number;

  @ApiPropertyOptional({ type: 'string', format: 'binary' })
  image?: Express.Multer.File;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  url?: string;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  dynamic_url?: boolean;

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  alt?: string;

  @ApiProperty()
  @IsString()
  position: string;
}

export class CreateBannerDto {
  @ApiProperty({ type: [ImageDto] })
  @IsArray()
  images: ImageDto[];

  @ApiPropertyOptional()
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ type: 'string' })
  @IsString()
  location: string;

  @ApiProperty({ type: 'string' })
  @IsString()
  device: string;

  @ApiPropertyOptional({ enum: BannerType })
  @IsString()
  @IsOptional()
  type?: BannerType;

  @ApiProperty()
  @IsString()
  position: string;
}

export class UpdateBannerDto extends CreateBannerDto {
  @ApiPropertyOptional()
  @IsOptional()
  listIdImageDelete: number[];
}

export class GetBannerListParamsDto extends ListRequest {
  @ApiPropertyOptional({ type: 'string', format: 'string' })
  @IsOptional()
  location?: string;

  @ApiPropertyOptional({ type: 'string', format: 'string' })
  @IsOptional()
  device?: string;
}
