import { PrismaService } from '@clout/prisma/prisma.service';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ApiStatus, BannerResponseDto, CreateBannerDto, GetBannerListParamsDto, UpdateBannerDto } from './banners.dto';
import { Configuration } from '@clout/common/config/configuration.interface';
import { S3Service } from '@clout/common/providers/s3/s3.service';
import { ConfigService } from '@nestjs/config';
import { Banner, Prisma } from '@prisma/client';

type BannerWithImages = Banner & {
  bannerImages: {
    image: {
      id: number;
      imageUrl: string;
      uploadedBy: number | null;
    };
    position: number;
    alt_image: string | null;
    url: string | null;
    updatedAt: Date;
    uploadAt: Date;
    dynamic_url: boolean;
  }[];
};


@Injectable()
export class BannersService {
  logger = new Logger(BannersService.name);
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
    private s3Service: S3Service,
  ) { }

  async getBanners(query: GetBannerListParamsDto) {
    const whereClause: Prisma.BannerWhereInput = {
      deleteAt: null,
    };

    if (query?.location) {
      whereClause.location = {
        mode: 'insensitive',
        contains: query.location,
      }
    }

    if (query?.device) {
      whereClause.device = {
        mode: 'insensitive',
        contains: query.device,
      }
    }

    const banners = await this.prisma.banner.findMany({
      include: {
        bannerImages: {
          include: {
            image: true,
          },
          orderBy: {
            position: "asc",
          }
        },
      },
      where: whereClause,
      orderBy: [
        {
          position: "asc"
        },
        {
          createdAt: "desc"
        },
      ]
    });

    const data = banners.map(item => {
      return this.formatBannerResponse(item);
    })

    return {
      data,
      status: ApiStatus.SUCCESS
    }
  }

  async getBannerById(id: number) {
    const banner = await this.prisma.banner.findFirst({
      include: {
        bannerImages: {
          include: {
            image: true,
          },
          orderBy: {
            position: "asc",
          }
        },
      },
      where: {
        id
      }
    });

    if (!banner) throw new NotFoundException("Banner not found")

    const data = await this.formatBannerResponse(banner);

    return {
      data,
      status: ApiStatus.SUCCESS
    }
  }

  async createBanner(userId: number, data: CreateBannerDto): Promise<BannerResponseDto> {
    const user = await this.prisma.user.findUnique({ where: { id: userId, OR: [{ isAdmin: true }, { isSuperAdmin: true }] } });
    if (!user) throw new ForbiddenException("Forbidden");

    const checkBanner = await this.prisma.banner.findFirst({
      where: {
        location: data.location,
        device: data.device,
      }
    })

    // if (checkBanner) {
    //   throw new ConflictException("Banner already exists for this location and device")
    // }

    try {
      for (const image of data.images) {
        const imageUrl = await this.transformMulterImageData(userId, image.image)

        if (imageUrl) {
          image['imageUrl'] = imageUrl
        }
      }

      const banner = await this.prisma.banner.create({
        data: {
          name: data?.name,
          location: data.location,
          device: data.device,
          type: data.type,
          position: +data.position,
        },
      })

      for (const image of data.images) {
        await this.prisma.bannerImage.create({
          data: {
            Banner: {
              connect: {
                id: banner.id,
              },
            },
            alt_image: image.alt,
            url: image.url,
            position: +image.position,
            image: {
              create: {
                imageUrl: image['imageUrl'],
                uploadedBy: userId,
              },
            },
          }
        })
      }

      if (banner && checkBanner) {
        await this.deleteBanner(userId, checkBanner.id);
      }

      return {
        data: banner,
        status: ApiStatus.SUCCESS
      }
    } catch (error) {
      console.log(error)
      this.logger.log(error)
      throw new BadRequestException(error)
    }
  }

  async updateBanner(userId: number, id: number, data: UpdateBannerDto): Promise<BannerResponseDto> {
    const user = await this.prisma.user.findUnique({ where: { id: userId, OR: [{ isAdmin: true }, { isSuperAdmin: true }] } });
    if (!user) throw new ForbiddenException("Forbidden");

    const checkBanner = await this.prisma.banner.findFirst({
      where: {
        id
      }
    })

    if (!checkBanner) {
      throw new NotFoundException("Banner not found")
    }

    try {
      for (const image of data.images) {
        if (image?.id && image?.image || !image?.id) {
          const imageUrl = await this.transformMulterImageData(userId, image.image)

          if (imageUrl) {
            image['imageUrl'] = imageUrl;
          }
        }
      }

      const imagesUpdate = data.images.filter(item => item?.id)
      const imagesCreate = data.images.filter(item => !item?.id)

      await this.prisma.$transaction(async (tx) => {
        if (imagesUpdate?.length) {
          for (const image of imagesUpdate) {
            if (image['imageUrl']) {
              await tx.image.update({
                where: {
                  id: +image.id
                },
                data: {
                  imageUrl: image['imageUrl']
                }
              })
            }

            const payload = {
              alt_image: image?.alt,
              url: image?.url,
              position: +image?.position
            }

            await tx.bannerImage.update({
              where: {
                imageId: +image.id
              },
              data: payload,
            })
          }
        }

        const payload = {
          name: data?.name,
          location: data?.location,
          device: data?.device,
          type: data?.type,
          position: data?.position ? +data?.position : checkBanner.position,
        }

        await tx.banner.update({
          where: {
            id,
          },
          data: payload,
        })


        if (imagesCreate.length) {
          for (const image of imagesCreate) {
            await this.prisma.bannerImage.create({
              data: {
                Banner: {
                  connect: {
                    id,
                  },
                },
                alt_image: image.alt,
                url: image.url,
                position: +image.position,
                image: {
                  create: {
                    imageUrl: image['imageUrl'],
                    uploadedBy: userId,
                  },
                },
              }
            })
          }
        }

        if (data?.listIdImageDelete?.length) {
          const bannerImage = await tx.bannerImage.findMany({
            where: {
              imageId: {
                in: data?.listIdImageDelete.map(item => +item)
              }
            }
          });

          if (bannerImage) {
            await tx.bannerImage.deleteMany({
              where: {
                id: {
                  in: bannerImage.map(item => item.id)
                }
              }
            });

            await tx.image.deleteMany({
              where: {
                id: {
                  in: bannerImage.map(item => item.imageId)
                }
              }
            });
          }
        }
      })

      return {
        status: ApiStatus.SUCCESS
      }
    } catch (error) {
      console.log(error)
      this.logger.log(error)
      throw new BadRequestException(error)
    }
  }

  async deleteBanner(userId: number, id: number) {
    const user = await this.prisma.user.findUnique({ where: { id: userId, OR: [{ isAdmin: true }, { isSuperAdmin: true }] } });
    if (!user) throw new ForbiddenException("Forbidden");

    const checkBanner = await this.prisma.banner.findFirst({
      where: {
        id
      }
    })

    if (!checkBanner) throw new NotFoundException("Banner not found")

    try {
      await this.prisma.$transaction(async (tx) => {
        const bannerImage = await tx.bannerImage.findMany({
          where: {
            bannerId: id,
          }
        });

        if (bannerImage) {
          await tx.bannerImage.deleteMany({
            where: {
              id: {
                in: bannerImage.map(item => item.id)
              }
            }
          });

          await tx.image.deleteMany({
            where: {
              id: {
                in: bannerImage.map(item => item.imageId)
              }
            }
          });
        }

        await tx.banner.delete({
          where: {
            id,
          },
        })
      })

      return {
        status: ApiStatus.SUCCESS
      }
    } catch (error) {
      console.log(error)
      this.logger.log(error)
      throw new BadRequestException(error)
    }
  }

  formatBannerResponse(banner: BannerWithImages) {
    return {
      id: banner.id,
      name: banner.name,
      location: banner.location,
      device: banner.device,
      type: banner.type,
      position: banner.position,
      createdAt: banner.createdAt,
      updatedAt: banner.updatedAt,
      images: banner.bannerImages?.map(bannerImage => ({
        id: bannerImage.image.id,
        imageUrl: bannerImage.image.imageUrl,
        uploadedBy: bannerImage.image.uploadedBy,
        position: +bannerImage.position,
        alt_image: bannerImage.alt_image,
        url: bannerImage.url,
        dynamic_url: bannerImage.dynamic_url,
        updatedAt: bannerImage.updatedAt,
        uploadAt: bannerImage.uploadAt,
      })) ?? [],
    };
  }

  private async transformMulterImageData(
    userId: number,
    image: Express.Multer.File
  ) {
    const date = new Date().toISOString().replace(/-|\.|:/g, '');
    const managementCode = `${userId}_${date}`;
    const randomName = Array(16)
      .fill(null)
      .map(() => Math.round(Math.random() * 16).toString(16))
      .join('');
    const bucketName =
      this.configService.get<Configuration['s3']['bucketName']>(
        's3.bucketName'
      );

    // Replace spaces with dashes in the original filename
    const sanitizedOriginalname = image.originalname.replace(/\s+/g, '-');

    const uploadFileParams = {
      name: managementCode + '-' + randomName + '-' + sanitizedOriginalname,
      body: image.buffer,
      bucket: bucketName,
    };
    const uploadedImage = await this.s3Service.upload(uploadFileParams);

    return uploadedImage.Location;
  }
}
