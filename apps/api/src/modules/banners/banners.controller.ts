import { BadRequestException, Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query, UploadedFiles, UseInterceptors } from "@nestjs/common";
import { ApiOkResponse, ApiTags } from "@nestjs/swagger";
import { BannersService } from "./banners.service";
import { CurrentUser } from "../../helpers/current-user-decorator";
import { AccessTokenParsed } from "../auth/auth.interface";
import { BannerResponseDto, BannersResponseDto, CreateBannerDto, GetBannerListParamsDto, ImageDto, UpdateBannerDto } from "./banners.dto";
import { Public } from "../auth/public.decorator";
import { AnyFilesInterceptor } from "@nestjs/platform-express";
import { plainToClass } from 'class-transformer';

@Controller('banners')
@ApiTags('bannersAPI')
export class BannersController {
  constructor(private readonly bannersService: BannersService) {}

  @Public()
  @Get()
  @ApiOkResponse({ type: BannersResponseDto })
  async getBanners(@Query() query: GetBannerListParamsDto): Promise<BannersResponseDto> {
    return this.bannersService.getBanners(query);
  }

  @Post()
  @ApiOkResponse({ type: BannerResponseDto })
  @UseInterceptors(AnyFilesInterceptor())
  async createBanner(@CurrentUser() user: AccessTokenParsed, @UploadedFiles() files: Array<Express.Multer.File>, @Body() body: any): Promise<BannerResponseDto> {
    const images: ImageDto[] = [];

      // Parse the body to extract the image details
      Object.keys(body).forEach(key => {
        const match = key.match(/^images\[(\d+)\]\.(.+)$/);
        if (match) {
          const index = parseInt(match[1], 10);
          const prop = match[2];

          if (!images[index]) {
            images[index] = plainToClass(ImageDto, {});
          }

          images[index][prop] = decodeURIComponent(body[key]);
        }
      });

      // Add the uploaded files to the correct image object
      files.forEach(file => {
        const match = file.fieldname.match(/^images\[(\d+)\]\.image$/);
        if (match) {
          const index = parseInt(match[1], 10);
          if (!images[index]) {
            images[index] = plainToClass(ImageDto, {});
          }
          images[index].image = file;
        }
      });

       // Validate that all required fields are present in the images array
      images.forEach(image => {
        if (!image.image) {
          throw new BadRequestException('Image is missing in one of the images');
        }
      });

      // Create the full DTO
      const createBannerDto: CreateBannerDto = {
        ...body,
        images,
      };

      return this.bannersService.createBanner(user.id, createBannerDto);
  }

  @Put(':id')
  @ApiOkResponse({ type: BannerResponseDto })
  @UseInterceptors(AnyFilesInterceptor())
  async updateBanner(@CurrentUser() user: AccessTokenParsed, @Param('id', ParseIntPipe) id: number, @UploadedFiles() files: Array<Express.Multer.File>, @Body() body: any): Promise<BannerResponseDto> {
    const images: ImageDto[] = [];

      // Parse the body to extract the image details
      Object.keys(body).forEach(key => {
        const match = key.match(/^images\[(\d+)\]\.(.+)$/);
        if (match) {
          const index = parseInt(match[1], 10);
          const prop = match[2];

          if (!images[index]) {
            images[index] = plainToClass(ImageDto, {});
          }

          images[index][prop] = decodeURIComponent(body[key]);
        }
      });

      // Add the uploaded files to the correct image object
      files.forEach(file => {
        const match = file.fieldname.match(/^images\[(\d+)\]\.image$/);
        if (match) {
          const index = parseInt(match[1], 10);
          if (!images[index]) {
            images[index] = plainToClass(ImageDto, {});
          }
          images[index].image = file;
        }
      });

       // Validate that all required fields are present in the images array
      images.forEach(image => {
        if (!image.image && !image.id) {
          throw new BadRequestException('Image is missing in one of the images');
        }
      });

      // Create the full DTO
      const updateBannerDto = plainToClass(UpdateBannerDto, { ...body, images });

      return this.bannersService.updateBanner(user.id, id, updateBannerDto);
  }

  @Public()
  @Get(':id')
  @ApiOkResponse({ type: BannerResponseDto })
  async getDetailBanner(@Param('id', ParseIntPipe) id: number,): Promise<BannerResponseDto> {
    return this.bannersService.getBannerById(id);
  }

  @Delete(':id')
  @ApiOkResponse({ type: BannerResponseDto })
  async deleteBanner(@CurrentUser() user: AccessTokenParsed, @Param('id', ParseIntPipe) id: number,): Promise<BannerResponseDto> {
    return this.bannersService.deleteBanner(user.id, id);
  }
}
