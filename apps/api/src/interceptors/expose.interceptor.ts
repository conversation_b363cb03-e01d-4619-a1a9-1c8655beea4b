/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

function expose(data) {
  if (Array.isArray(data)) {
    for (let i = 0; i < data.length; i++) {
      data[i] = expose(data[i]);
    }
  } else if (typeof data === 'object' && data !== null) {
    delete data.password;
    delete data.twoFactorSecret;
    delete data.token;
    delete data.subnet;
    Object.keys(data).forEach((key) => {
      data[key] = expose(data[key]);
    });
  }
  return data;
}
@Injectable()
export class ExposeInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        return expose(data);
      })
    );
  }
}
