/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { Configuration } from '@clout/common/config/configuration.interface';
import { RATE_LIMIT_EXCEEDED } from '@clout/common/constants/errors.constants';
import { RateLimiterMemory } from 'rate-limiter-flexible';
import { getClientIp } from 'request-ip';
import { Observable } from 'rxjs';
import { UserRequest } from '../modules/auth/auth.interface';

@Injectable()
export class RateLimitInterceptor implements NestInterceptor {
  private rateLimiterHourlyPublic = new RateLimiterMemory(
    this.configService.get<Configuration['rateLimit']['public']['hourly']>(
      'rateLimit.public.hourly'
    )
  );
  private rateLimiterMinutelyPublic = new RateLimiterMemory(
    this.configService.get<Configuration['rateLimit']['public']['minutely']>(
      'rateLimit.public.minutely'
    )
  );
  private rateLimiterHourlyAuthenticated = new RateLimiterMemory(
    this.configService.get<Configuration['rateLimit']['authenticated']['hourly']>(
      'rateLimit.authenticated.hourly'
    )
  );
  private rateLimiterMinutelyAuthenticated = new RateLimiterMemory(
    this.configService.get<Configuration['rateLimit']['authenticated']['minutely']>(
      'rateLimit.authenticated.minutely',
    )
  );
  private rateLimiterHourlyApiKey = new RateLimiterMemory(
    this.configService.get<Configuration['rateLimit']['apiKey']['hourly']>(
      'rateLimit.apiKey.hourly'
    )
  );
  private rateLimiterMinutelyApiKey = new RateLimiterMemory(
    this.configService.get<Configuration['rateLimit']['apiKey']['minutely']>(
      'rateLimit.apiKey.minutely',
    )
  );
  constructor(
    private readonly reflector: Reflector,
    private configService: ConfigService
  ) { }

  async intercept(
    context: ExecutionContext,
    next: CallHandler
  ): Promise<Observable<any>> {
    const points =
      this.reflector.get<number>('rateLimit', context.getHandler()) ?? 1;
    const request = context.switchToHttp().getRequest() as UserRequest;
    const response = context.switchToHttp().getResponse();

    let limiterHourly = this.rateLimiterHourlyPublic;
    let limiterMinutely = this.rateLimiterMinutelyPublic;
    if (request.user?.type === 'api-key') {
      limiterHourly = this.rateLimiterHourlyApiKey;
      limiterMinutely = this.rateLimiterMinutelyApiKey;
    } else if (request.user?.type === 'user') {
      limiterHourly = this.rateLimiterHourlyAuthenticated;
      limiterMinutely = this.rateLimiterMinutelyAuthenticated;
    }

    try {
      const ip = getClientIp(request).replace(/^.*:/, '');
      const resultHourly = await limiterHourly.consume(ip, points);
      const resultMinutely = await limiterMinutely.consume(ip, points);
      response.header('Retry-After-Hourly', Math.ceil(resultHourly.msBeforeNext / 1000));
      response.header('Retry-After-Minutely', Math.ceil(resultMinutely.msBeforeNext / 1000));
      response.header('X-RateLimit-Limit-Hourly', limiterHourly.points);
      response.header('X-RateLimit-Limit-Minutely', limiterMinutely.points);
      response.header('X-Retry-Remaining-Hourly', resultHourly.remainingPoints);
      response.header('X-Retry-Remaining-Minutely', resultMinutely.remainingPoints);
      response.header('X-Retry-Reset-Hourly', new Date(Date.now() + resultHourly.msBeforeNext).toUTCString());
      response.header('X-Retry-Reset-Minutely', new Date(Date.now() + resultMinutely.msBeforeNext).toUTCString());
    } catch (result) {
      response.header('Retry-After', Math.ceil(result.msBeforeNext / 1000));
      throw new HttpException(
        {
          statusCode: 429,
          error: RATE_LIMIT_EXCEEDED,
          code: "429001",
        },
        HttpStatus.TOO_MANY_REQUESTS
      );
    }
    return next.handle();
  }
}
