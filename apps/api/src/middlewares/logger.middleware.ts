import { Injectable, NestMiddleware } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  constructor(private readonly configService: ConfigService) {}
  use(req: Request, res: Response, next: CallableFunction): void {
    // TODO: use this.logger.log
    //Hơi ngược môi trường vì ban đầu để novu ở development, khách đang dùng nên k dám sửa ._.
    let novuApiKey = this.configService.get('NOVU_DEVELOP_API_KEY');
    if (req.headers['isautotest']) {
      novuApiKey = this.configService.get('NOVU_PROD_API_KEY');
    }
    req.headers['novuApiKey'] = novuApiKey;

    next();
  }
}
