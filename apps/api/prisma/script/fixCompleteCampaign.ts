// eslint-disable-next-line @typescript-eslint/no-var-requires
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();
async function main() {
  const result = await prisma.campaign.updateMany({
    where : {
      status: "COMPLETION",
      endReason: null
    },
    data: {
      endReason: "OTHER"
    }
  })

  console.log(result)
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => prisma.$disconnect);
