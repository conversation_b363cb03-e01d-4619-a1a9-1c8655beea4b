generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-arm64-openssl-3.0.x", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator dbml {
  provider = "prisma-dbml-generator"
}

enum StatusMaintenance {
  Completed
  In_Progress
  Not_Started
}

enum BannerType {
  Carousel
  Rotation
  Fixed
}

model Company {
  id               Int           @id @default(autoincrement())
  name             String?
  code             String?
  emailId          Int?          @unique
  email            Email?        @relation("companyEmail", fields: [emailId], references: [id])
  imageId          Int?          @unique
  image            Image?        @relation("companyImage", fields: [imageId], references: [id])
  member           User[]        @relation("memberCompany")
  campaign         Campaign[]    @relation("companyCampaign")
  createdAt        DateTime      @default(now())
  updatedAt        DateTime      @updatedAt
  deletedAt        DateTime?
  companyRole      CompanyRole[] @relation("companyRoleUser")
  payment          Payment[]     @relation("companyPayment")
  cardInfo         Json?
  squareCustomerId String?
  squareCardId     String?
  novuId           String?
  pointTotal       Float?        @default(0)

  @@index([emailId])
}

model CompanyRole {
  companyId  Int
  company    Company    @relation("companyRoleUser", fields: [companyId], references: [id])
  userId     Int        @unique
  user       User       @relation("userCompanyRole", fields: [userId], references: [id])
  membership Membership
  isVerified Boolean    @default(true)
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  deletedAt  DateTime?

  @@id([userId, companyId])
  @@index([userId])
}

model User {
  id                  Int                 @id
  createdAt           DateTime            @default(now())
  //
  isAdmin             Boolean             @default(false)
  // supper admin để không cần phải xin tài khoản cty bị lỗi để debug
  isSuperAdmin        Boolean             @default(false)
  name                String
  notificationEmail   NotificationEmail   @default(ACCOUNT)
  password            String?
  prefersLanguage     String              @default("en-us")
  emailId             Int?
  profilePictureUrl   String              @default("https://unavatar.now.sh/fallback.png")
  timezone            String              @default("Asia/Tokyo")
  twoFactorMethod     MfaMethod           @default(NONE)
  twoFactorPhone      String?
  twoFactorSecret     String?
  updatedAt           DateTime            @updatedAt
  uuid                String              @unique @default(uuid())
  deleteFlg           Boolean             @default(false)
  deletedAt           DateTime?
  lastActive          DateTime?           @default(now())
  isVerified          Boolean             @default(false)
  email               Email?              @relation("userEmail", fields: [emailId], references: [id])
  backupCodes         BackupCode[]        @relation("userBackupCode")
  identities          Identity[]          @relation("userIdentity")
  userNotification    Notification[]      @relation("userNotification")
  NotificationToken   NotificationToken[] @relation("notificationToken")
  sessions            Session[]           @relation("userSession")
  Campaign            Campaign[]          @relation("createdUserCampaign")
  UserCampaign        UserCampaign[]      @relation("userCampaign")
  Payment             Payment[]           @relation("userPayment")
  UserTask            UserTask[]          @relation("userTask")
  sharedTasks         UserTask[]          @relation("userShareTask")
  sharedCampaignViews CampaignView[]      @relation("userShareCampaign")
  companyId           Int?
  memberCompany       Company?            @relation("memberCompany", fields: [companyId], references: [id])
  companyRole         CompanyRole?        @relation("userCompanyRole")
  pointTotal          Float?              @default(0)
  CampaignView        CampaignView[]      @relation("userCampaignView")

  @@index([emailId])
}

model CampaignView {
  id          Int   @id @default(autoincrement())
  userId      Int
  user        User  @relation("userCampaignView", fields: [userId], references: [id])
  userShareId Int?
  userShare   User? @relation("userShareCampaign", fields: [userShareId], references: [id])

  campaignId String
  Campaign   Campaign @relation("campaignCampaignView", fields: [campaignId], references: [id])

  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  @@index([userId, campaignId])
}

model UserTask {
  id          Int   @id @default(autoincrement())
  userId      Int
  user        User  @relation("userTask", fields: [userId], references: [id])
  taskId      Int
  task        Task  @relation(fields: [taskId], references: [id])
  userShareId Int?
  userShare   User? @relation("userShareTask", fields: [userShareId], references: [id])

  answer    Json?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  @@index([userId, taskId])
}

model Coupon {
  id            Int         @id @default(autoincrement())
  code          String
  type          CouponType  @default(AMAZON_GIFT)
  userAwardId   Int         @unique
  userAward     UserAward   @relation("userAwardCoupon", fields: [userAwardId], references: [id])
  transactionId Int         @unique()
  transaction   Transaction @relation("couponTransaction", fields: [transactionId], references: [id])
  updatedAt     DateTime    @updatedAt
  createdAt     DateTime    @default(now())

  @@index([userAwardId])
}

model Payment {
  id                     String       @id @default(uuid())
  amount                 Float
  pointUse               Float?       @default(0)
  trace_id               String
  extra                  Json?
  userId                 Int
  user                   User         @relation("userPayment", fields: [userId], references: [id])
  companyId              Int
  company                Company      @relation("companyPayment", fields: [companyId], references: [id])
  campaignId             String?
  campaignName           String?
  updatedAt              DateTime     @updatedAt
  createdAt              DateTime     @default(now())
  type                   PaymentType? @default(PAYMENT)
  amountAfterTransaction Float?
}

model Transaction {
  id          Int        @id @default(autoincrement())
  type        RewardType
  extra       Json?
  beforeValue Int?
  afterValue  Int?
  coupon      Coupon?    @relation("couponTransaction")
  updatedAt   DateTime   @updatedAt
  createdAt   DateTime   @default(now())
}

model Campaign {
  id                 String             @id @default(uuid())
  status             CampaignStatus     @default(DRAFT)
  category           String? // master data
  title              String?
  description        String?            @db.Text
  imageId            Int?               @unique
  priority           Int                @default(0)
  createdUserId      Int
  createdUser        User               @relation("createdUserCampaign", fields: [createdUserId], references: [id])
  companyId          Int
  company            Company            @relation("companyCampaign", fields: [companyId], references: [id])
  maxTimeUserClaim   Int                @default(1)
  claimEndTime       DateTime?
  expiredTime        DateTime?
  startTime          DateTime?
  dontSetExpiredTime Boolean            @default(false)
  setExpiredTime     Boolean            @default(false)
  endReason          CampaignEndReason?

  //side reward
  methodOfselectWinners           MethodOfselectWinners?
  totalNumberOfUsersAllowedToWork Int?
  numberOfPrizes                  Int?
  totalPrizeValue                 Int?
  settingForNotWin                Boolean                @default(false)
  settingForWin                   Boolean                @default(false)
  noteReward                      String?                @db.Text

  updatedAt         DateTime         @updatedAt
  createdAt         DateTime         @default(now())
  Task              Task[]           @relation("taskCampaign")
  UserClaimCampaign UserCampaign[]   @relation("campaignUser")
  CampaignReward    CampaignReward[] @relation("campaignReward")
  image             Image?           @relation("campaignImage", fields: [imageId], references: [id])
  totalViews        Int?             @default(0)
  emailUserUpdated  String?
  idUserUpdated     Int?
  timeUpdated       DateTime?
  isWaitingPurcare  Boolean          @default(false)
  CampaignView      CampaignView[]   @relation("campaignCampaignView")
}

model Task {
  id             Int          @id @default(autoincrement())
  campaignId     String
  campaign       Campaign     @relation("taskCampaign", fields: [campaignId], references: [id])
  type           TaskType
  // required       Boolean      @default(false)
  // points         Int          @default(0)
  taskActionType String? // master data follow, view, ...
  taskTemplateId Int
  taskTemplate   TaskTemplate @relation("taskTemplate", fields: [taskTemplateId], references: [id])
  UserTask       UserTask[]
  updatedAt      DateTime     @updatedAt
  createdAt      DateTime     @default(now())
}

model UserCampaign {
  id                  Int        @id @default(autoincrement())
  userId              Int
  campaignId          String
  user                User       @relation("userCampaign", fields: [userId], references: [id])
  campaign            Campaign   @relation("campaignUser", fields: [campaignId], references: [id])
  identityAccountName String?
  award               UserAward? @relation("userAward")
  updatedAt           DateTime   @updatedAt
  createdAt           DateTime   @default(now())

  @@unique([userId, campaignId])
  @@index([userId, campaignId])
}

model TaskTemplate {
  id        Int      @id @default(autoincrement())
  userName  String?
  extra     Json?
  config    Json?
  link      String?
  quote     String?  @db.Text
  //todo: update required,points => tasks
  required  Boolean  @default(true)
  points    Int      @default(0)
  updatedAt DateTime @updatedAt
  createdAt DateTime @default(now())
  Task      Task[]   @relation("taskTemplate")
}

model CampaignReward {
  id                    Int                 @id @default(autoincrement())
  title                 String?
  type                  CampaignRewardType?
  campaignId            String
  campaign              Campaign            @relation("campaignReward", fields: [campaignId], references: [id])
  index                 Int
  amountOfMoney         Float               @default(0)
  numberOfWinningTicket Int                 @default(0)
  userAward             UserAward[]         @relation("campaignUserAward")
  updatedAt             DateTime            @updatedAt
  deleteAt              DateTime?
  createdAt             DateTime            @default(now())
}

model MaintenancePeriod {
  id          Int               @id @default(autoincrement())
  startAt     DateTime
  endAt       DateTime
  description String?
  status      StatusMaintenance @default(Not_Started)

  updatedAt DateTime  @updatedAt
  deleteAt  DateTime?
  createdAt DateTime  @default(now())
}

model Banner {
  id       Int         @id @default(autoincrement())
  name     String?
  location String
  device   String
  type     BannerType? @default(Carousel)
  position Int

  bannerImages BannerImage[] @relation("bannerImageBanner")

  updatedAt DateTime  @updatedAt
  deleteAt  DateTime?
  createdAt DateTime  @default(now())
}

model BannerImage {
  id Int @id @default(autoincrement())

  url       String?
  alt_image String?
  position  Int
  dynamic_url Boolean  @default(false)

  imageId  Int    @unique
  image    Image  @relation("bannerImageImage", fields: [imageId], references: [id], onDelete: Cascade)
  bannerId Int
  Banner   Banner @relation("bannerImageBanner", fields: [bannerId], references: [id])

  uploadAt  DateTime @default(now())
  deleteFlg Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model UserAward {
  id               Int             @id @default(autoincrement())
  campaignRewardId Int?
  campaignReward   CampaignReward? @relation("campaignUserAward", fields: [campaignRewardId], references: [id])
  userCampaignId   Int             @unique
  userCampaign     UserCampaign    @relation("userAward", fields: [userCampaignId], references: [id])
  value            Int
  coupon           Coupon?         @relation("userAwardCoupon")
  isWin            String?
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  deletedAt        DateTime?
  isFinalPrize     Boolean         @default(false)

  @@index([userCampaignId, campaignRewardId])
}

model Email {
  id         Int       @id @default(autoincrement())
  email      String    @unique
  isVerified Boolean   @default(false)
  userId     Int?
  user       User[]    @relation("userEmail")
  companyId  Int?
  company    Company[] @relation("companyEmail")
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  deletedAt  DateTime?
}

model Identity {
  id          Int          @id @default(autoincrement())
  userId      Int
  user        User         @relation("userIdentity", fields: [userId], references: [id])
  type        IdentityType
  accountId   String
  accountName String?
  avatar      String?
  token       String?      @db.Text
  info        Json?
  active      Boolean      @default(true)

  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?

  @@index([userId])
}

model BackupCode {
  id        Int      @id @default(autoincrement())
  code      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  isUsed    Boolean  @default(false)
  userId    Int
  user      User     @relation("userBackupCode", fields: [userId], references: [id])

  @@index([userId])
}

model NotificationToken {
  id        Int       @id @default(autoincrement())
  userId    Int
  token     String    @db.Text
  deleteFlg Boolean   @default(false)
  deletedAt DateTime?
  updatedAt DateTime  @updatedAt
  user      User      @relation("notificationToken", fields: [userId], references: [id])

  @@index([userId])
}

model Notification {
  id        Int                    @id @default(autoincrement())
  userId    Int
  content   String?                @db.Text
  isSeen    Boolean                @default(false)
  deleteFlg Boolean                @default(false)
  deletedAt DateTime?
  createdBy Int?
  createdAt DateTime               @default(now())
  updatedBy Int?
  updatedAt DateTime               @updatedAt
  title     String                 @db.Text
  action    NotificationActionType @default(TEXT)
  user      User                   @relation("userNotification", fields: [userId], references: [id])

  @@index([userId])
}

model Session {
  createdAt       DateTime @default(now())
  id              Int      @id @default(autoincrement())
  ipAddress       String
  token           String
  updatedAt       DateTime @updatedAt
  userAgent       String?
  city            String?
  region          String?
  timezone        String?
  countryCode     String?
  browser         String?
  operatingSystem String?
  userId          Int
  user            User     @relation("userSession", fields: [userId], references: [id])

  @@index([userId])
}

model Image {
  id       Int    @id @default(autoincrement())
  imageUrl String @unique

  campaign    Campaign?    @relation("campaignImage")
  company     Company?     @relation("companyImage")
  bannerImage BannerImage? @relation("bannerImageImage")

  uploadedBy Int?
  uploadAt   DateTime @default(now())

  deleteFlg Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model SystemMetadata {
  id    Int     @id @default(autoincrement())
  index Int?
  value String
  group String?
  label String
}

enum CampaignStatus {
  DRAFT
  WAITING_FOR_PURCASE
  UNDER_REVIEW
  WAITING_FOR_PUBLICATION
  PUBLIC
  COMPLETION
}

enum CampaignEndReason {
  WINNER_LIMIT_REACHED
  OTHER
}

enum NotificationActionType {
  CHAT
  PROFILE
  TEXT
}

enum IdentityType {
  TWITTER
  TIKTOK
  LINE
  TELEGRAM
  DISCORD
}

enum MfaMethod {
  NONE
  TOTP
  EMAIL
}

enum Membership {
  HAVE_NOT_ACCEPTED
  MANAGER
  MEMBER
  ROOT
}

enum NotificationEmail {
  ACCOUNT
  UPDATES
  PROMOTIONS
}

enum TaskType {
  TWITTER
  TIKTOK
  LINE
  INSTAGRAM
  TELEGRAM
  DISCORD
  VISIT_PAGE
  CUSTOM
  SHARE_URL
}

enum RewardType {
  AMAZON_GIFT
  PAYPAY_GIFT
  POINT
}

enum CampaignRewardType {
  AMAZON_GIFT
  PAYPAY_GIFT
  BOTH
}

enum MethodOfselectWinners {
  AUTO_PRIZEE_DRAW
  MANUAL_SELECTION
}

enum CouponType {
  AMAZON_GIFT
  PAYPAY_GIFT
}

enum PaymentType {
  PAYMENT
  WITHDRAWAL
}
