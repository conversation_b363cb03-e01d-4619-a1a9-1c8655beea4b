/*
  Warnings:

  - You are about to drop the column `status` on the `UserTask` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "UserTask" DROP COLUMN "status";

-- DropEnum
DROP TYPE "UserTaskStatus";

-- CreateTable
CREATE TABLE "CampaignView" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "userShareId" INTEGER,
    "campaignId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "CampaignView_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CampaignView_userId_campaignId_idx" ON "CampaignView"("userId", "campaignId");

-- AddForeignKey
ALTER TABLE "CampaignView" ADD CONSTRAINT "CampaignView_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CampaignView" ADD CONSTRAINT "CampaignView_userShareId_fkey" FOREIGN KEY ("userShareId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CampaignView" ADD CONSTRAINT "CampaignView_campaignId_fkey" FOREIGN KEY ("campaignId") REFERENCES "Campaign"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
