/*
  Warnings:

  - You are about to drop the column `isOther` on the `UserTask` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[userShareId]` on the table `UserTask` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "UserTask" DROP COLUMN "isOther",
ADD COLUMN     "userShareId" INTEGER;

-- CreateIndex
CREATE UNIQUE INDEX "UserTask_userShareId_key" ON "UserTask"("userShareId");

-- AddForeignKey
ALTER TABLE "UserTask" ADD CONSTRAINT "UserTask_userShareId_fkey" FOREIGN KEY ("userShareId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
