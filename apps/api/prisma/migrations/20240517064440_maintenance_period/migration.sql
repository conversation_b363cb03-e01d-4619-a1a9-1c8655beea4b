-- CreateEnum
CREATE TYPE "StatusMaintenance" AS ENUM ('Completed', 'In_Progress', 'Not_Started');

-- CreateTable
CREATE TABLE "MaintenancePeriod" (
    "id" SERIAL NOT NULL,
    "startAt" TIMESTAMP(3) NOT NULL,
    "endAt" TIMESTAMP(3) NOT NULL,
    "description" TEXT,
    "status" "StatusMaintenance" NOT NULL DEFAULT 'Not_Started',
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleteAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "MaintenancePeriod_pkey" PRIMARY KEY ("id")
);
