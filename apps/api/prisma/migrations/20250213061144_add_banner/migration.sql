/*
  Warnings:

  - A unique constraint covering the columns `[imageUrl]` on the table `Image` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateEnum
CREATE TYPE "BannerType" AS ENUM ('Carousel', 'Rotation', 'Fixed');

-- CreateTable
CREATE TABLE "Banner" (
    "id" SERIAL NOT NULL,
    "name" TEXT,
    "location" TEXT NOT NULL,
    "device" TEXT NOT NULL,
    "type" "BannerType" DEFAULT 'Carousel',
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deleteAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Banner_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BannerImage" (
    "id" SERIAL NOT NULL,
    "url" TEXT,
    "alt_image" TEXT,
    "position" INTEGER NOT NULL,
    "imageId" INTEGER NOT NULL,
    "bannerId" INTEGER NOT NULL,
    "uploadAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleteFlg" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BannerImage_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "BannerImage_imageId_key" ON "BannerImage"("imageId");

-- CreateIndex
CREATE UNIQUE INDEX "Image_imageUrl_key" ON "Image"("imageUrl");

-- AddForeignKey
ALTER TABLE "BannerImage" ADD CONSTRAINT "BannerImage_imageId_fkey" FOREIGN KEY ("imageId") REFERENCES "Image"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BannerImage" ADD CONSTRAINT "BannerImage_bannerId_fkey" FOREIGN KEY ("bannerId") REFERENCES "Banner"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
