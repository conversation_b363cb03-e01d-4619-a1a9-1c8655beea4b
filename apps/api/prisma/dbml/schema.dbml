//// ------------------------------------------------------
//// THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
//// ------------------------------------------------------

Table Company {
  id Int [pk, increment]
  name String
  code String
  emailId Int [unique]
  email Email
  imageId Int [unique]
  image Image
  member User [not null]
  campaign Campaign [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
  deletedAt DateTime
  companyRole CompanyRole [not null]
  payment Payment [not null]
  cardInfo Json
  squareCustomerId String
  squareCardId String
  novuId String
  pointTotal Float [default: 0]
}

Table CompanyRole {
  companyId Int [not null]
  company Company [not null]
  userId Int [unique, not null]
  user User [not null]
  membership Membership [not null]
  isVerified Boolean [not null, default: true]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
  deletedAt DateTime

  indexes {
    (userId, companyId) [pk]
  }
}

Table User {
  id Int [pk]
  createdAt DateTime [default: `now()`, not null]
  isAdmin Boolean [not null, default: false]
  isSuperAdmin Boolean [not null, default: false]
  name String [not null]
  notificationEmail NotificationEmail [not null, default: 'ACCOUNT']
  password String
  prefersLanguage String [not null, default: 'en-us']
  emailId Int
  profilePictureUrl String [not null, default: 'https://unavatar.now.sh/fallback.png']
  timezone String [not null, default: 'Asia/Tokyo']
  twoFactorMethod MfaMethod [not null, default: 'NONE']
  twoFactorPhone String
  twoFactorSecret String
  updatedAt DateTime [not null]
  uuid String [unique, not null]
  deleteFlg Boolean [not null, default: false]
  deletedAt DateTime
  lastActive DateTime [default: `now()`]
  isVerified Boolean [not null, default: false]
  email Email
  backupCodes BackupCode [not null]
  identities Identity [not null]
  userNotification Notification [not null]
  NotificationToken NotificationToken [not null]
  sessions Session [not null]
  Campaign Campaign [not null]
  UserCampaign UserCampaign [not null]
  Payment Payment [not null]
  UserTask UserTask [not null]
  sharedTasks UserTask [not null]
  sharedCampaignViews CampaignView [not null]
  companyId Int
  memberCompany Company
  companyRole CompanyRole
  pointTotal Float [default: 0]
  CampaignView CampaignView [not null]
}

Table CampaignView {
  id Int [pk, increment]
  userId Int [not null]
  user User [not null]
  userShareId Int
  userShare User
  campaignId String [not null]
  Campaign Campaign [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
  deletedAt DateTime
}

Table UserTask {
  id Int [pk, increment]
  userId Int [not null]
  user User [not null]
  taskId Int [not null]
  task Task [not null]
  userShareId Int
  userShare User
  answer Json
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
  deletedAt DateTime
}

Table Coupon {
  id Int [pk, increment]
  code String [not null]
  type CouponType [not null, default: 'AMAZON_GIFT']
  userAwardId Int [unique, not null]
  userAward UserAward [not null]
  transactionId Int [unique, not null]
  transaction Transaction [not null]
  updatedAt DateTime [not null]
  createdAt DateTime [default: `now()`, not null]
}

Table Payment {
  id String [pk]
  amount Float [not null]
  pointUse Float [default: 0]
  trace_id String [not null]
  extra Json
  userId Int [not null]
  user User [not null]
  companyId Int [not null]
  company Company [not null]
  campaignId String
  campaignName String
  updatedAt DateTime [not null]
  createdAt DateTime [default: `now()`, not null]
  type PaymentType [default: 'PAYMENT']
  amountAfterTransaction Float
}

Table Transaction {
  id Int [pk, increment]
  type RewardType [not null]
  extra Json
  beforeValue Int
  afterValue Int
  coupon Coupon
  updatedAt DateTime [not null]
  createdAt DateTime [default: `now()`, not null]
}

Table Campaign {
  id String [pk]
  status CampaignStatus [not null, default: 'DRAFT']
  category String
  title String
  description String
  imageId Int [unique]
  priority Int [not null, default: 0]
  createdUserId Int [not null]
  createdUser User [not null]
  companyId Int [not null]
  company Company [not null]
  maxTimeUserClaim Int [not null, default: 1]
  claimEndTime DateTime
  expiredTime DateTime
  startTime DateTime
  dontSetExpiredTime Boolean [not null, default: false]
  setExpiredTime Boolean [not null, default: false]
  endReason CampaignEndReason
  methodOfselectWinners MethodOfselectWinners
  totalNumberOfUsersAllowedToWork Int
  numberOfPrizes Int
  totalPrizeValue Int
  settingForNotWin Boolean [not null, default: false]
  settingForWin Boolean [not null, default: false]
  noteReward String
  updatedAt DateTime [not null]
  createdAt DateTime [default: `now()`, not null]
  Task Task [not null]
  UserClaimCampaign UserCampaign [not null]
  CampaignReward CampaignReward [not null]
  image Image
  totalViews Int [default: 0]
  emailUserUpdated String
  idUserUpdated Int
  timeUpdated DateTime
  isWaitingPurcare Boolean [not null, default: false]
  CampaignView CampaignView [not null]
}

Table Task {
  id Int [pk, increment]
  campaignId String [not null]
  campaign Campaign [not null]
  type TaskType [not null]
  taskActionType String
  taskTemplateId Int [not null]
  taskTemplate TaskTemplate [not null]
  UserTask UserTask [not null]
  updatedAt DateTime [not null]
  createdAt DateTime [default: `now()`, not null]
}

Table UserCampaign {
  id Int [pk, increment]
  userId Int [not null]
  campaignId String [not null]
  user User [not null]
  campaign Campaign [not null]
  identityAccountName String
  award UserAward
  updatedAt DateTime [not null]
  createdAt DateTime [default: `now()`, not null]

  indexes {
    (userId, campaignId) [unique]
  }
}

Table TaskTemplate {
  id Int [pk, increment]
  userName String
  extra Json
  config Json
  link String
  quote String
  required Boolean [not null, default: true]
  points Int [not null, default: 0]
  updatedAt DateTime [not null]
  createdAt DateTime [default: `now()`, not null]
  Task Task [not null]
}

Table CampaignReward {
  id Int [pk, increment]
  title String
  type CampaignRewardType
  campaignId String [not null]
  campaign Campaign [not null]
  index Int [not null]
  amountOfMoney Float [not null, default: 0]
  numberOfWinningTicket Int [not null, default: 0]
  userAward UserAward [not null]
  updatedAt DateTime [not null]
  deleteAt DateTime
  createdAt DateTime [default: `now()`, not null]
}

Table MaintenancePeriod {
  id Int [pk, increment]
  startAt DateTime [not null]
  endAt DateTime [not null]
  description String
  status StatusMaintenance [not null, default: 'Not_Started']
  updatedAt DateTime [not null]
  deleteAt DateTime
  createdAt DateTime [default: `now()`, not null]
}

Table Banner {
  id Int [pk, increment]
  name String
  location String [not null]
  device String [not null]
  type BannerType [default: 'Carousel']
  position Int [not null]
  bannerImages BannerImage [not null]
  updatedAt DateTime [not null]
  deleteAt DateTime
  createdAt DateTime [default: `now()`, not null]
}

Table BannerImage {
  id Int [pk, increment]
  url String
  alt_image String
  position Int [not null]
  dynamic_url Boolean [not null, default: false]
  imageId Int [unique, not null]
  image Image [not null]
  bannerId Int [not null]
  Banner Banner [not null]
  uploadAt DateTime [default: `now()`, not null]
  deleteFlg Boolean [not null, default: false]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table UserAward {
  id Int [pk, increment]
  campaignRewardId Int
  campaignReward CampaignReward
  userCampaignId Int [unique, not null]
  userCampaign UserCampaign [not null]
  value Int [not null]
  coupon Coupon
  isWin String
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
  deletedAt DateTime
  isFinalPrize Boolean [not null, default: false]
}

Table Email {
  id Int [pk, increment]
  email String [unique, not null]
  isVerified Boolean [not null, default: false]
  userId Int
  user User [not null]
  companyId Int
  company Company [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
  deletedAt DateTime
}

Table Identity {
  id Int [pk, increment]
  userId Int [not null]
  user User [not null]
  type IdentityType [not null]
  accountId String [not null]
  accountName String
  avatar String
  token String
  info Json
  active Boolean [not null, default: true]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
  deletedAt DateTime
}

Table BackupCode {
  id Int [pk, increment]
  code String [not null]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
  isUsed Boolean [not null, default: false]
  userId Int [not null]
  user User [not null]
}

Table NotificationToken {
  id Int [pk, increment]
  userId Int [not null]
  token String [not null]
  deleteFlg Boolean [not null, default: false]
  deletedAt DateTime
  updatedAt DateTime [not null]
  user User [not null]
}

Table Notification {
  id Int [pk, increment]
  userId Int [not null]
  content String
  isSeen Boolean [not null, default: false]
  deleteFlg Boolean [not null, default: false]
  deletedAt DateTime
  createdBy Int
  createdAt DateTime [default: `now()`, not null]
  updatedBy Int
  updatedAt DateTime [not null]
  title String [not null]
  action NotificationActionType [not null, default: 'TEXT']
  user User [not null]
}

Table Session {
  createdAt DateTime [default: `now()`, not null]
  id Int [pk, increment]
  ipAddress String [not null]
  token String [not null]
  updatedAt DateTime [not null]
  userAgent String
  city String
  region String
  timezone String
  countryCode String
  browser String
  operatingSystem String
  userId Int [not null]
  user User [not null]
}

Table Image {
  id Int [pk, increment]
  imageUrl String [unique, not null]
  campaign Campaign
  company Company
  bannerImage BannerImage
  uploadedBy Int
  uploadAt DateTime [default: `now()`, not null]
  deleteFlg Boolean [not null, default: false]
  createdAt DateTime [default: `now()`, not null]
  updatedAt DateTime [not null]
}

Table SystemMetadata {
  id Int [pk, increment]
  index Int
  value String [not null]
  group String
  label String [not null]
}

Enum StatusMaintenance {
  Completed
  In_Progress
  Not_Started
}

Enum BannerType {
  Carousel
  Rotation
  Fixed
}

Enum CampaignStatus {
  DRAFT
  WAITING_FOR_PURCASE
  UNDER_REVIEW
  WAITING_FOR_PUBLICATION
  PUBLIC
  COMPLETION
}

Enum CampaignEndReason {
  WINNER_LIMIT_REACHED
  OTHER
}

Enum NotificationActionType {
  CHAT
  PROFILE
  TEXT
}

Enum IdentityType {
  TWITTER
  TIKTOK
  LINE
  TELEGRAM
  DISCORD
}

Enum MfaMethod {
  NONE
  TOTP
  EMAIL
}

Enum Membership {
  HAVE_NOT_ACCEPTED
  MANAGER
  MEMBER
  ROOT
}

Enum NotificationEmail {
  ACCOUNT
  UPDATES
  PROMOTIONS
}

Enum TaskType {
  TWITTER
  TIKTOK
  LINE
  INSTAGRAM
  TELEGRAM
  DISCORD
  VISIT_PAGE
  CUSTOM
  SHARE_URL
}

Enum RewardType {
  AMAZON_GIFT
  PAYPAY_GIFT
  POINT
}

Enum CampaignRewardType {
  AMAZON_GIFT
  PAYPAY_GIFT
  BOTH
}

Enum MethodOfselectWinners {
  AUTO_PRIZEE_DRAW
  MANUAL_SELECTION
}

Enum CouponType {
  AMAZON_GIFT
  PAYPAY_GIFT
}

Enum PaymentType {
  PAYMENT
  WITHDRAWAL
}

Ref: Company.emailId > Email.id

Ref: Company.imageId - Image.id

Ref: CompanyRole.companyId > Company.id

Ref: CompanyRole.userId - User.id

Ref: User.emailId > Email.id

Ref: User.companyId > Company.id

Ref: CampaignView.userId > User.id

Ref: CampaignView.userShareId > User.id

Ref: CampaignView.campaignId > Campaign.id

Ref: UserTask.userId > User.id

Ref: UserTask.taskId > Task.id

Ref: UserTask.userShareId > User.id

Ref: Coupon.userAwardId - UserAward.id

Ref: Coupon.transactionId - Transaction.id

Ref: Payment.userId > User.id

Ref: Payment.companyId > Company.id

Ref: Campaign.createdUserId > User.id

Ref: Campaign.companyId > Company.id

Ref: Campaign.imageId - Image.id

Ref: Task.campaignId > Campaign.id

Ref: Task.taskTemplateId > TaskTemplate.id

Ref: UserCampaign.userId > User.id

Ref: UserCampaign.campaignId > Campaign.id

Ref: CampaignReward.campaignId > Campaign.id

Ref: BannerImage.imageId - Image.id [delete: Cascade]

Ref: BannerImage.bannerId > Banner.id

Ref: UserAward.campaignRewardId > CampaignReward.id

Ref: UserAward.userCampaignId - UserCampaign.id

Ref: Identity.userId > User.id

Ref: BackupCode.userId > User.id

Ref: NotificationToken.userId > User.id

Ref: Notification.userId > User.id

Ref: Session.userId > User.id