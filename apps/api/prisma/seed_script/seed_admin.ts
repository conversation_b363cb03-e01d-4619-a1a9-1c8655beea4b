const { PrismaClient } = require('@prisma/client');
const { hash } = require('bcrypt');
const prisma = new PrismaClient();
async function main() {
  const listData = [
    {
      email: '<EMAIL>',
      password: 'ApxvelYT4ENwHmm4',
    },
    {
      email: '<EMAIL>',
      password: 'JMPQARe29bRHge0',
    }
  ];

  for (const data of listData) {
    const checkEmail = await prisma.email.findFirst({
      where: { email: data.email },
    });

    if (!checkEmail || !checkEmail?.userId) {
      const passwordHash: string = await hash(data.password, 10);

      let id;
      while (!id) {
        id = Number(Math.random() * 10);
        const users = await prisma.user.findMany({ where: { id }, take: 1 });
        if (users.length) id = undefined;
      }

      const areaCode = Math.floor(100 + Math.random() * 900);

      const firstPart = Math.floor(100 + Math.random() * 900);

      const secondPart = Math.floor(1000 + Math.random() * 9000);

      const newUser = await prisma.user.create({
        data: {
          id,
          name: '',
          password: passwordHash,
          isVerified: true,
          twoFactorMethod: data.email === '<EMAIL>' ? 'TOTP' : 'NONE',
          twoFactorPhone: data.email === '<EMAIL>' ? `(${areaCode}) ${firstPart}-${secondPart}` : '',
          isAdmin: data.email === '<EMAIL>' ? true : false,
          isSuperAdmin: data.email === '<EMAIL>' ? true : false
        },
      });
      if (newUser) {
        let emailIdAdd = null;
        if (checkEmail) {
          await prisma.email.update({
            where: { id: checkEmail.id },
            data: { userId: newUser.id },
          });
          emailIdAdd = checkEmail.id;
        } else {
          const newEmail = await prisma.email.create({
            data: {
              email: data.email,
              userId: id,
            },
          });
          emailIdAdd = newEmail.id;
        }

        await prisma.user.update({
          where: { id: newUser.id },
          data: { emailId: emailIdAdd },
        });
      }
    }
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => prisma.$disconnect);
