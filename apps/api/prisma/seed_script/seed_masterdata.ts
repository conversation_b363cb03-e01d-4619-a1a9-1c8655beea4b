// eslint-disable-next-line @typescript-eslint/no-var-requires
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  const PROVINCE = [
    { index: 5, value: '5', group: 'PROVINCE', label: '秋田県' },
    { index: 7, value: '7', group: 'PROVINCE', label: '福島県' },
    { index: 8, value: '8', group: 'PROVINCE', label: '茨城県' },
    { index: 9, value: '9', group: 'PROVINCE', label: '栃木県' },
    { index: 12, value: '12', group: 'PROVINCE', label: '千葉県' },
    { index: 13, value: '13', group: 'PROVINCE', label: '東京都' },
    { index: 15, value: '15', group: 'PROVINCE', label: '新潟県' },
    { index: 16, value: '16', group: 'PROVINCE', label: '富山県' },
    { index: 17, value: '17', group: 'PROVINCE', label: '石川県' },
    { index: 19, value: '19', group: 'PROVINCE', label: '山梨県' },
    { index: 21, value: '21', group: 'PROVINCE', label: '岐阜県' },
    { index: 22, value: '22', group: 'PROVINCE', label: '静岡県' },
    { index: 28, value: '28', group: 'PROVINCE', label: '兵庫県' },
    { index: 29, value: '29', group: 'PROVINCE', label: '奈良県' },
    { index: 30, value: '30', group: 'PROVINCE', label: '和歌山県' },
    { index: 33, value: '33', group: 'PROVINCE', label: '岡山県' },
    { index: 36, value: '36', group: 'PROVINCE', label: '徳島県' },
    { index: 37, value: '37', group: 'PROVINCE', label: '香川県' },
    { index: 38, value: '38', group: 'PROVINCE', label: '愛媛県' },
    { index: 41, value: '41', group: 'PROVINCE', label: '佐賀県' },
    { index: 42, value: '42', group: 'PROVINCE', label: '長崎県' },
    { index: 44, value: '44', group: 'PROVINCE', label: '大分県' },
    { index: 1, value: '1', group: 'PROVINCE', label: '北海道' },
    { index: 2, value: '2', group: 'PROVINCE', label: '青森県' },
    { index: 3, value: '3', group: 'PROVINCE', label: '岩手県' },
    { index: 4, value: '4', group: 'PROVINCE', label: '宮城県' },
    { index: 6, value: '6', group: 'PROVINCE', label: '山形県' },
    { index: 10, value: '10', group: 'PROVINCE', label: '群馬県' },
    { index: 11, value: '11', group: 'PROVINCE', label: '埼玉県' },
    { index: 14, value: '14', group: 'PROVINCE', label: '神奈川県' },
    { index: 18, value: '18', group: 'PROVINCE', label: '福井県' },
    { index: 20, value: '20', group: 'PROVINCE', label: '長野県' },
    { index: 23, value: '23', group: 'PROVINCE', label: '愛知県' },
    { index: 24, value: '24', group: 'PROVINCE', label: '三重県' },
    { index: 25, value: '25', group: 'PROVINCE', label: '滋賀県' },
    { index: 26, value: '26', group: 'PROVINCE', label: '京都府' },
    { index: 27, value: '27', group: 'PROVINCE', label: '大阪府' },
    { index: 31, value: '31', group: 'PROVINCE', label: '鳥取県' },
    { index: 32, value: '32', group: 'PROVINCE', label: '島根県' },
    { index: 34, value: '34', group: 'PROVINCE', label: '広島県' },
    { index: 35, value: '35', group: 'PROVINCE', label: '山口県' },
    { index: 39, value: '39', group: 'PROVINCE', label: '高知県' },
    { index: 40, value: '40', group: 'PROVINCE', label: '福岡県' },
    { index: 43, value: '43', group: 'PROVINCE', label: '熊本県' },
    { index: 45, value: '45', group: 'PROVINCE', label: '宮崎県' },
    { index: 46, value: '46', group: 'PROVINCE', label: '鹿児島県' },
    { index: 47, value: '47', group: 'PROVINCE', label: '沖縄県' },
  ];

  const CATEGORY_CAMPAIGN = [
    { index: 1, value: 'art', group: 'CATEGORY_CAMPAIGN', label: 'アート' },
    {
      index: 2,
      value: 'entertainment',
      group: 'CATEGORY_CAMPAIGN',
      label: 'エンターテイメント',
    },
    { index: 3, value: 'car', group: 'CATEGORY_CAMPAIGN', label: '自動車' },
    {
      index: 4,
      value: 'business',
      group: 'CATEGORY_CAMPAIGN',
      label: 'ビジネス',
    },
    {
      index: 5,
      value: 'career',
      group: 'CATEGORY_CAMPAIGN',
      label: 'キャリア',
    },
    {
      index: 6,
      value: 'education',
      group: 'CATEGORY_CAMPAIGN',
      label: '教育',
    },
    {
      index: 7,
      value: 'family_and_parenting',
      group: 'CATEGORY_CAMPAIGN',
      label: '家族＆子育て',
    },
    {
      index: 8,
      value: 'health_and_fitness',
      group: 'CATEGORY_CAMPAIGN',
      label: '健康＆フィットネス',
    },
    {
      index: 9,
      value: 'food_and_drink',
      group: 'CATEGORY_CAMPAIGN',
      label: 'フード＆ドリンク',
    },
    {
      index: 10,
      value: 'hobby',
      group: 'CATEGORY_CAMPAIGN',
      label: '趣味',
    },
    {
      index: 11,
      value: 'housing',
      group: 'CATEGORY_CAMPAIGN',
      label: '住宅',
    },
    {
      index: 12,
      value: 'garden',
      group: 'CATEGORY_CAMPAIGN',
      label: 'ガーデン',
    },
    { index: 13, value: 'law', group: 'CATEGORY_CAMPAIGN', label: '法律' },
    {
      index: 14,
      value: 'politics',
      group: 'CATEGORY_CAMPAIGN',
      label: '政治',
    },
    { index: 15, value: 'news', group: 'CATEGORY_CAMPAIGN', label: 'ニュース' },
    {
      index: 16,
      value: 'finance',
      group: 'CATEGORY_CAMPAIGN',
      label: '金融',
    },
    {
      index: 17,
      value: 'society',
      group: 'CATEGORY_CAMPAIGN',
      label: '社会',
    },
    {
      index: 18,
      value: 'chemistry',
      group: 'CATEGORY_CAMPAIGN',
      label: '化学',
    },
    { index: 19, value: 'pet', group: 'CATEGORY_CAMPAIGN', label: 'ペット' },
    {
      index: 20,
      value: 'sports',
      group: 'CATEGORY_CAMPAIGN',
      label: 'スポーツ',
    },
    {
      index: 21,
      value: 'fashion',
      group: 'CATEGORY_CAMPAIGN',
      label: 'ファッション',
    },
    {
      index: 22,
      value: 'technology',
      group: 'CATEGORY_CAMPAIGN',
      label: 'テクノロジー',
    },
    {
      index: 23,
      value: 'trip',
      group: 'CATEGORY_CAMPAIGN',
      label: '旅行',
    },
    {
      index: 24,
      value: 'real_estate',
      group: 'CATEGORY_CAMPAIGN',
      label: '不動産',
    },
    {
      index: 25,
      value: 'shopping',
      group: 'CATEGORY_CAMPAIGN',
      label: 'ショッピング',
    },
    {
      index: 26,
      value: 'religion',
      group: 'CATEGORY_CAMPAIGN',
      label: '宗教',
    },
    {
      index: 27,
      value: 'fortune_telling',
      group: 'CATEGORY_CAMPAIGN',
      label: '占い',
    },
    {
      index: 28,
      value: 'spiritual',
      group: 'CATEGORY_CAMPAIGN',
      label: 'スピリチュアル',
    },
    {
      index: 29,
      value: 'token',
      group: 'CATEGORY_CAMPAIGN',
      label: 'トークン',
    },
    {
      index: 30,
      value: 'nft',
      group: 'CATEGORY_CAMPAIGN',
      label: 'NFT',
    },
    {
      index: 31,
      value: 'others',
      group: 'CATEGORY_CAMPAIGN',
      label: 'その他',
    },
  ];

  const TASK_ACTION = [
    {
      index: 1,
      value: 'twitter_follow',
      group: 'TWITTER_ACTION',
      label: 'フォローさせる',
    },
    {
      index: 2,
      value: 'twitter_repost',
      group: 'TWITTER_ACTION',
      label: 'リポストさせる',
    },
    {
      index: 3,
      value: 'twitter_repost_quote',
      group: 'TWITTER_ACTION',
      label: '引用リポストさせる',
    },
    {
      index: 4,
      value: 'twitter_make_post_with_hashtags',
      group: 'TWITTER_ACTION',
      label: '指定ハッシュタグ付きの投稿をさせる',
    },
    {
      index: 5,
      value: 'twitter_make_post',
      group: 'TWITTER_ACTION',
      label: '指定文言を投稿させる',
    },
    {
      index: 1,
      value: 'tiktok_watch',
      group: 'TIKTOK_ACTION',
      label: '視聴させる',
    },
    {
      index: 2,
      value: 'tiktok_follow',
      group: 'TIKTOK_ACTION',
      label: 'フォローさせる',
    },
    {
      index: 1,
      value: 'telegram_join_channel',
      group: 'TELEGRAM_ACTION',
      label: 'チャンネルに参加させる',
    },
    {
      index: 2,
      value: 'telegram_view_posts',
      group: 'TELEGRAM_ACTION',
      label: '投稿を閲覧させる',
    },
    {
      index: 1,
      value: 'discord_invite',
      group: 'DISCORD_ACTION',
      label: 'サーバーに招待する',
    },
    {
      index: 1,
      value: 'selection',
      group: 'CUSTOM_ACTION',
      label: '選択形式',
    },
    {
      index: 2,
      value: 'free_answer',
      group: 'CUSTOM_ACTION',
      label: '自由回答',
    },
  ];

  await prisma.systemMetadata.deleteMany({ where: {} });
  await prisma.systemMetadata.createMany({
    data: [...PROVINCE, ...CATEGORY_CAMPAIGN, ...TASK_ACTION].map((item) => {
      return {
        ...item,
      };
    }),
  });
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => prisma.$disconnect);
