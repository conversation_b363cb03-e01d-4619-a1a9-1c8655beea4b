{"name": "api-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "implicitDependencies": ["api"], "projectType": "application", "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/api-e2e/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/api-e2e/**/*.{js,ts}"]}}}}