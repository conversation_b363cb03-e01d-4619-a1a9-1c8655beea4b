stages:
  - docker_install_dependencies
  - review
  # develop
  - update-database-develop
  - reload-docker-compose-develop
  # staging
  - release_staging
  - rollback_staging
  # production
  - release_production
  - rollback_production
  #
  - push_changelog
  - release_tag
  #
  - test

include:
  - project: 'khanh99/monitor-pjs'
    ref: main
    file: '.gitlab-ci.yml'
  # Staging develop
  - local: .gitlab/develop-deploy.yml
    rules:
      - if: $CI_COMMIT_BRANCH == "develop"
  # Staging deploy
  - local: .gitlab/staging-deploy.yml
    rules:
      - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "staging"
  # Auto test deploy
  - local: .gitlab/auto-test-deploy.yml
    rules:
      - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "staging"
  # # Production deploy
  # - local: .gitlab/production-deploy.yml
  #   rules:
  #     - if: $CI_COMMIT_BRANCH == "main"
