stages:
  - test

# Define push changelog rules
.push_changelog_rules: &push_changelog_rules
  - if: '$CI_COMMIT_MESSAGE =~ /CHANGELOG\.md/i'
    when: never
  - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
    when: always
  - when: never

.test:
  image: node:18-alpine
  stage: test
  cache:
    key: $CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
    policy: pull
  before_script:
    - apk update && apk add openssh-client bash rsync yarn sshpass
    - apk --no-cache --virtual build-dependencies add python3 make g++
    - eval $(ssh-agent -s)
    - bash -c 'ssh-add <(echo "$SSH_PRIVATE_KEY")'
    - mkdir -p ~/.ssh
    - ssh-keyscan -p $SSH_PORT -H $SSH_SERVER_IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - pwd
    - cp .env.$CI_ENVIRONMENT_NAME.example .env
    - yarn
    - node ./newman.test.js
  # tags:
  #   - local_sv

staging_test:
  extends: .test
  rules: *push_changelog_rules
  environment:
    name: staging
