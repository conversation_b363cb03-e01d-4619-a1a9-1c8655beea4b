stages:
  - update-database-develop
  - reload-docker-compose-develop

.update-database-develop:
  stage: update-database-develop
  image: postgres:15
  before_script:
    - apt-get update && apt-get install -y rsync curl bash openssh-client sshpass
    - curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    - apt-get install -y nodejs
    - npm install -g yarn
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - ssh-keyscan -p $SSH_PORT -H $SSH_SERVER_IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - yarn
    - sshpass -p $SSH_PASSWORD rsync -a -e "ssh -p $SSH_PORT" node_modules/ $SSH_USER@$SSH_SERVER_IP:$REMOTE_PROJECT_DIR/node_modules
    - sshpass -p $SSH_PASSWORD ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "pwd; source ~/.nvm/nvm.sh; cd $REMOTE_PROJECT_DIR; git pull; rm .env; cp .env.$CI_ENVIRONMENT_NAME.example .env; yarn prisma migrate deploy; yarn prisma generate"
  only:
    changes:
      - apps/api/prisma/schema.prisma
      - .gitlab-ci.yml
  # tags:
  #   - local_sv

.reload-docker-compose-develop:
  stage: reload-docker-compose-develop
  image:
    name: alpine:latest
  before_script:
    - apk update && apk add openssh-client bash rsync yarn sshpass
    - apk --no-cache --virtual build-dependencies add python3 make g++
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - ssh-keyscan -H $SSH_SERVER_IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - pwd
    - sshpass -p $SSH_PASSWORD ssh -T $SSH_USER@$SSH_SERVER_IP "pwd; cd $REMOTE_PROJECT_DIR; git pull; cp .env.$CI_ENVIRONMENT_NAME.example .env;sh ./start.sh;"
  # tags:
  #   - local_sv

reload-docker-compose-develop:
  extends: .reload-docker-compose-develop
  only:
    refs:
      - develop
  environment:
    name: develop
  interruptible: true

update-database-develop:
  extends: .update-database-develop
  only:
    refs:
      - develop
  environment:
    name: develop
