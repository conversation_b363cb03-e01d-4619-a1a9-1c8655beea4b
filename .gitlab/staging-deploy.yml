stages:
  - release_staging
  - rollback_staging

# Define push changelog rules
.push_changelog_rules: &push_changelog_rules
  - if: '$CI_COMMIT_MESSAGE =~ /CHANGELOG\.md/i'
    when: never
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "staging"
    when: always
  - when: never

# Define push changelog rules
.push_changelog_rules_manual: &push_changelog_rules_manual
  - if: '$CI_COMMIT_MESSAGE =~ /CHANGELOG\.md/i'
    when: never
  - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH == "staging"
    when: manual
  - when: never

.release_staging:
  stage: release_staging
  image:
    name: node:18-alpine
  before_script:
    - apk update && apk add openssh-client bash rsync yarn sshpass
    - apk --no-cache --virtual build-dependencies add python3 make g++
    - eval $(ssh-agent -s)
    - bash -c 'ssh-add <(echo "$SSH_PRIVATE_KEY")'
    - mkdir -p ~/.ssh
    - ssh-keyscan -p $SSH_PORT -H $SSH_SERVER_IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "pwd; cd $REMOTE_PROJECT_DIR; git pull;"

    - echo $SSH_SERVER_IP
    - rm -rf .env
    - cp .env.$CI_ENVIRONMENT_NAME.example .env
    - yarn install
    - yarn prisma generate
    - yarn api:build

    - rsync -a -e "ssh -p $SSH_PORT" node_modules/ $SSH_USER@$SSH_SERVER_IP:$REMOTE_PROJECT_DIR/node_modules
    - rsync -a -e "ssh -p $SSH_PORT" dist/ $SSH_USER@$SSH_SERVER_IP:$REMOTE_PROJECT_DIR/dist
    - rsync -a -e "ssh -p $SSH_PORT" .env $SSH_USER@$SSH_SERVER_IP:$REMOTE_PROJECT_DIR/.env

    - ssh -T -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "pwd; source ~/.nvm/nvm.sh; cd $REMOTE_PROJECT_DIR; yarn prisma migrate deploy; pm2 restart clout-api;"
  # tags:
  #   - local_sv

release_staging:
  extends: .release_staging
  rules: *push_changelog_rules
  environment:
    name: staging
