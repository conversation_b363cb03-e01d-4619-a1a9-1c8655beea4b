require('dotenv').config();
const newman = require('newman');
const FormData = require('form-data');
const fs = require('fs');
const axios = require('axios');

newman
  .run(
    {
      collection: process.env.NEWMAN_COLLECTION_PATH,
      // environment: process.env.NEWMAN_ENVIRONMENT_PATH,
      reporters: ['cli', 'htmlextra'],
      reporter: {
        htmlextra: {
          export: process.env.NEWMAN_EXPORT_PATH,
          browserTitle: process.env.NEWMAN_TITLE,
          title: process.env.NEWMAN_TITLE,
          titleSize: 4,
          displayProgressBar: true,
        },
      },
      workingDir: process.env.NEWMAN_WORK_DIR,
    },
    (err) => console.log(err)
  )
  .on('start', () => {
    console.log(`Running a collection. Please wait a few seconds...`);
  })
  .on('done', async (err, res) => {
    console.log('=>> done');
    const { stats, timings } = res.run;
    let readStream = fs.createReadStream(process.env.NEWMAN_EXPORT_PATH);
    let form = new FormData();
    form.append('file', readStream);
    form.append('channels', process.env.SLACK_CHANNEL_ID);
    form.append(
      'initial_comment',
      `
      ${'`'}Time: ${new Date().toLocaleString('VN', {
        timeZone: 'Asia/Ho_Chi_Minh',
      })}${'`'}

      cc: ${process.env.USERS_TAG}

      *[${process.env.NODE_ENV}]* Clout test result:
      - Total requests: ${'`'}${stats.requests.total}${'`'}
      - Total assertions: ${'`'}${stats.assertions.total}${'`'}
      - Total assertions failed: ${'`'}${stats.assertions.failed}${'`'}
      - Total assertions pass: ${'`'}${
        stats.assertions.total - stats.assertions.failed
      }${'`'}
      - Average response time: ${'`'}${
        timings.responseAverage.toFixed(0) +
        'ms [min: ' +
        timings.responseMin +
        'ms, max: ' +
        timings.responseMax +
        'ms, s.d.: ' +
        timings.responseSd.toFixed(0) +
        'ms]'
      }${'`'}
      - Total run duration: ${'`'}${(
        (timings.completed - timings.started) /
        1000
      ).toFixed(1)}s${'`'}
      `
    );
    form.append('title', 'Report File');
    //https://slack.com/api/files.upload
    axios
      .post('https://slack.com/api/files.upload', form, {
        headers: {
          Authorization: `Bearer ${process.env.SLACK_BOT_AUTH_TOKEN}`,
          'Content-Type': 'multipart/form-data',
        },
      })
      .then(function (response) {
        var serverMessage = response.data;
        console.log(serverMessage);
        console.log('Send file to slack successfully!');
      })
      .catch((error) => console.log('error axios send file', error));
  });
return;
