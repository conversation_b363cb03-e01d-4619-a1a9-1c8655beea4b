{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "esModuleInterop": true, "target": "es2017", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@clout/prisma/*": ["libs/prisma/src/lib/*"], "@clout/prisma": ["libs/prisma/src/lib"], "@clout/common/*": ["libs/common/src/*"], "@clout/common": ["libs/common/src"]}}}