###################
# BUILD FOR PRODUCTION
###################

FROM node:18-slim As build

WORKDIR /usr/src/app


COPY --chown=node:node package.json yarn.lock ./
COPY --chown=node:node apps/api/prisma ./apps/api/prisma/

# Running `npm ci` removes the existing node_modules directory.
# Passing in --only=production ensures that only the production dependencies are installed.
# This ensures that the node_modules directory is as optimized as possible.

RUN yarn install --frozen-lockfile

RUN yarn prisma generate


COPY --chown=node:node . .

# Run the build command which creates the production bundle
RUN yarn api:build


# Set NODE_ENV environment variable
# ENV NODE_ENV production

# RUN yarn install --frozen-lockfile --production && yarn cache clean

USER node
###################
# PRODUCTION
###################

FROM node:18-slim As production

# Create app directory
WORKDIR /usr/src/app

RUN apt-get update
RUN apt-get install -y openssl

# Copy the bundled code from the build stage to the production image
COPY --chown=node:node --from=build /usr/src/app/package.json ./package.json
COPY --chown=node:node --from=build /usr/src/app/node_modules ./node_modules
COPY --chown=node:node --from=build /usr/src/app/dist ./dist
# COPY --chown=node:node --from=build /usr/src/app/static ./static 

# Start the server using the production build
CMD [ "node", "dist/out-tsc/apps/api/src/main.js" ]
