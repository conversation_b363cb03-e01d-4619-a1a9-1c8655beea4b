import { Injectable } from '@nestjs/common';
import { connect, Connection, Channel } from 'amqplib';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class RabbitMQService {
  private connection: Connection;
  private channel: Channel;

  constructor(private readonly configService: ConfigService) {}

  async init() {
    const rabbitmqURL = this.configService.get<string>('RABBITMQ_URL');
    this.connection = await connect(rabbitmqURL);
    this.channel = await this.connection.createChannel();
  }

  getChannel(): Channel {
    return this.channel;
  }

  async close() {
    await this.channel.close();
    await this.connection.close();
  }
}
