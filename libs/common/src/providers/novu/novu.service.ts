import { Injectable, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { Novu } from '@novu/node';

@Injectable()
export class NovuService extends Novu implements OnModuleInit, OnModuleDestroy {
  constructor() {
    super(process.env.NOVU_DEVELOP_API_KEY);
  }
  async onModuleInit() {}

  async onModuleDestroy() {}

  // async sendMail(
  //   triggerId: string,
  //   subscriberId: string,
  //   email: string,
  //   payload: any,
  //   overrides?: any
  // ) {
  //   console.log({ triggerId, subscriberId, email, payload, overrides });

  //   const sendByNoVu = await this.trigger(triggerId || '', {
  //     to: {
  //       subscriberId: subscriberId,
  //       email: email,
  //     },
  //     payload,
  //     overrides,
  //   });
  //   console.log({ sendByNoVu: sendByNoVu?.data });

  //   return { status: true };
  // }
}
