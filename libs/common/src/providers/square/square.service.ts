import { Configuration } from '@clout/common/config/configuration.interface';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { randomUUID } from 'crypto';
import { Client, Environment } from 'square';

@Injectable()
export class SquareService {
  private config: Configuration['square'];
  client?: Client;
  constructor(private configService: ConfigService) {
    this.config = this.configService.get<Configuration['square']>('square');
    this.client = new Client({
      accessToken: this.config.accessToken,
      environment:
        process.env.SQUARE_ENV == 'production'
          ? Environment.Production
          : Environment.Sandbox,
      httpClientOptions: {
        timeout: 10000,
        retryConfig: {
          maxNumberOfRetries: 2,
          maximumRetryWaitTime: 1000000,
        },
      },
    });
  }

  async createCustomer(companyName: string) {
    try {
      const { customersApi } = this?.client;
      const createCustomerResponse = await customersApi.createCustomer({
        companyName,
        idempotencyKey: randomUUID(),
      });
      const customer = createCustomerResponse.result.customer;
      return { status: true, customerId: customer.id };
    } catch (error) {
      console.log({ error });
      return { status: false, error };
    }
  }

  async retrieveCard(cardId: string) {
    try {
      const { cardsApi } = this?.client;
      const retrieveCardResponse = await cardsApi.retrieveCard(cardId);
      const card = retrieveCardResponse.result.card;
      return { status: true, cardId: card.id };
    } catch (error) {
      console.log({ error });
      return { status: false, error };
    }
  }

  async createCard(sourceId: string, customerId: string) {
    try {
      const { cardsApi } = this?.client;
      const createCardResponse = await cardsApi.createCard({
        idempotencyKey: randomUUID(),
        sourceId,
        card: {
          customerId,
        },
      });
      const card = createCardResponse.result.card;
      return { status: true, cardId: card.id };
    } catch (error) {
      console.log({ error });
      return { status: false, error };
    }
  }

  async disableCard(cardId: string) {
    try {
      const { cardsApi } = this?.client;
      const disableCardResponse = await cardsApi.disableCard(cardId);
      const card = disableCardResponse.result.card;
      return { status: true, cardId: card.id };
    } catch (error) {
      console.log({ error });
      return { status: false, error };
    }
  }

  async createPayment(customerId: string, cardId: string, amount: number) {
    const { paymentsApi } = this.client;
    try {
      const paymentRequest = {
        idempotencyKey: randomUUID(),
        sourceId: cardId,
        // sourceId: "cnon:card-nonce-declined", // test for declined
        amountMoney: {
          currency: 'JPY',
          amount: BigInt(amount),
        },
        autocomplete: false, // Tạo tạm giữ mà không trừ tiền ngay lập tức
        customerId,
        locationId: this.config.locationId,
      };

      const createPaymentResponse = await paymentsApi.createPayment(paymentRequest);
      const payment = createPaymentResponse?.result?.payment;

      console.log("Payment created successfully:", payment);

      if (payment && payment.status == 'APPROVED') {
        const completedPayment = await paymentsApi.completePayment(payment.id, {
          versionToken: payment?.versionToken
        });
        console.log("Completed payment:", completedPayment);
        return { status: true, payment };
      } else {
        const cancel = await paymentsApi.cancelPayment(payment.id);
        return { status: false, error: cancel };
      }

    } catch (error) {
      console.log('Payment process encountered an error:', { error });

      const errorBody = typeof error?.body === 'string' ? JSON.parse(error.body) : error.body

      console.log('Error body:', errorBody);

      // Kiểm tra lỗi và huỷ thanh toán tạm giữ
      if (errorBody.errors?.find(e => e.category === 'PAYMENT_METHOD_ERROR')) {
        const { payment } = errorBody;
        const paymentResponse1 = await paymentsApi.getPayment(payment.id);
        console.log("Payment from error:", payment);
        console.log("paymentResponse from error:", paymentResponse1.result.payment);
        if (payment?.id) {
          const cancel = await paymentsApi.cancelPayment(payment.id);
          console.log("Cancel payment response:", cancel);

          const paymentResponse = await paymentsApi.getPayment(payment.id);

          console.log(`Payment with ID ${payment.id} has been cancelled.`, paymentResponse.result.payment);
        }
      }

      return { status: false, error: errorBody };
    }
  }
}
