import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as aws4 from 'aws4';
import axios, { AxiosHeaders } from 'axios';
import { IncentivesAPI } from './amazon_gift_card.namespace';
import { Configuration } from '@clout/common/config/configuration.interface';

@Injectable()
export class AmazonGiftCardService {
  private config: Configuration['amazonGiftCard'];
  private partnerId: string;
  private accessKeyId: string;
  private secretAccessKey: string;
  private endpoint: IncentivesAPI.Endpoint;
  constructor(private configService: ConfigService) {
    this.config =
      this.configService.get<Configuration['amazonGiftCard']>('amazonGiftCard');
    this.partnerId = this.config.partnerId;
    this.accessKeyId = this.config.accessKeyId;
    this.secretAccessKey = this.config.secretAccessKey;
    this.endpoint = this.config.endpoint;
  }
  /**
   * Creates a live gift card claim code and deducts the amount from the pre-payment account.
   * The `creationRequestId` must start from `partnerId`.
   * The `creationRequestId` must not exceed 40 characters.
   * See details: https://developer.amazon.com/ja/docs/incentives-api/digital-gift-cards.html#creategiftcard
   */
  async createGiftCard(
    params: {
      creationRequestId: string;
      currencyCode: string;
      amount: number;
    },
    options?: {
      transactionSource?: Object;
      sourceId?: string;
      institutionId?: string;
      sourceDetails?: string;
      programId?: string;
      productType?: string;
      externalReference?: string;
    }
  ): Promise<IncentivesAPI.CreateGiftCardResponse> {
    const createGiftCardRequest: IncentivesAPI.CreateGiftCardRequest = {
      creationRequestId: params.creationRequestId,
      partnerId: this.partnerId,
      value: {
        amount: params.amount,
        currencyCode: params.currencyCode,
      },
      ...options,
    };
    return this.sendAwsSignedRequest<
      IncentivesAPI.CreateGiftCardRequest,
      IncentivesAPI.CreateGiftCardResponse
    >('CreateGiftCard', createGiftCardRequest);
  }

  createRequestId(text: string) {
    const date = new Date().toISOString().replace(/-|\.|:/g, '');
    return `${this.config.partnerId}_${text}_${date}`;
  }

  /**
   * Cancel a gift card, as long as the gift card is not claimed by an Amazon customer.
   * Important: This operation can only be started within 15 minutes of the creation request time stamp.
   * See details: https://developer.amazon.com/ja/docs/incentives-api/digital-gift-cards.html#cancelgiftcard
   */
  async cancelGiftCard(
    creationRequestId: string
  ): Promise<IncentivesAPI.CancelGiftCardResponse> {
    const createGiftCardRequest: IncentivesAPI.CancelGiftCardRequest = {
      creationRequestId: creationRequestId,
      partnerId: this.partnerId,
    };
    return this.sendAwsSignedRequest<
      IncentivesAPI.CancelGiftCardRequest,
      IncentivesAPI.CancelGiftCardResponse
    >('CancelGiftCard', createGiftCardRequest);
  }

  private async sendAwsSignedRequest<Request, Response>(
    operation: 'CreateGiftCard' | 'CancelGiftCard',
    requestParams: Request
  ): Promise<Response> {
    const signedRequest = aws4.sign(
      {
        host: this.endpoint.host,
        region: this.endpoint.region,
        path: `/${operation}`,
        body: JSON.stringify(requestParams),
        service: 'AGCODService',
        headers: {
          accept: 'application/json',
          'content-type': 'application/json',
          'x-amz-target': `com.amazonaws.agcod.AGCODService./${operation}`,
        },
      },
      {
        accessKeyId: this.accessKeyId,
        secretAccessKey: this.secretAccessKey,
      }
    );

    const response = await axios.post<Response>(
      `https://${signedRequest.host}${signedRequest.path}`,
      signedRequest.body,
      {
        headers: signedRequest.headers as AxiosHeaders,
      }
    );

    return response.data;
  }
}

/*
 * See details: https://developer.amazon.com/ja/docs/incentives-api/incentives-api.html#endpoints
 */
