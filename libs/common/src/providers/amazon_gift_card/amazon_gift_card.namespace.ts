export namespace IncentivesAPI {
  // Naming convention follows the the scratchpad: https://s3.amazonaws.com/AGCOD/htmlSDKv2/htmlSDKv2_NAEUFE/index.html
  export class Endpoint {
    // Sandbox Endpoints
    static NorthAmericaSandbox = new Endpoint(
      'agcod-v2-gamma.amazon.com',
      'us-east-1'
    );

    static EuropeSandbox = new Endpoint(
      'agcod-v2-eu-gamma.amazon.com',
      'eu-west-1'
    );

    static JapanSandbox = new Endpoint(
      'agcod-v2-fe-gamma.amazon.com',
      'us-west-2'
    );

    // Production Endpoints
    static NorthAmericaProduction = new Endpoint(
      'agcod-v2.amazon.com',
      'us-east-1'
    );

    static EuropeProduction = new Endpoint(
      'agcod-v2-eu.amazon.com',
      'eu-west-1'
    );

    static JapanProduction = new Endpoint(
      'agcod-v2-fe.amazon.com',
      'us-west-2'
    );
    constructor(public host: string, public region: string) {}
  }

  /**
   * See details: https://developer.amazon.com/ja/docs/incentives-api/digital-gift-cards.html#requests
   */
  export interface CreateGiftCardRequest {
    creationRequestId: string;
    partnerId: string;
    value: GiftCardValue;
    /// B&M Only
    transactionSource?: Object | null;
    sourceId?: string | null;
    institutionId?: string | null;
    sourceDetails?: string | null;
    /// Reseller Only
    programId?: string | null;
    /// PV Use Case Only
    productType?: string | null;

    /// Optional
    externalReference?: string | null;
  }

  /**
   * See details: https://developer.amazon.com/ja/docs/incentives-api/digital-gift-cards.html#responses
   */
  export interface CreateGiftCardResponse {
    creationRequestId: string;
    cardInfo: GiftCardInfo;
    gcClaimCode: string;
    gcId: string;
    gcExpirationDate: string;
    status: string;
  }

  export interface GiftCardInfo {
    cardStatus: string;
    value: GiftCardValue;
  }

  export interface GiftCardValue {
    currencyCode: string;
    amount: number;
  }

  /**
   * See details: https://developer.amazon.com/ja/docs/incentives-api/digital-gift-cards.html#requests-1
   */
  export interface CancelGiftCardRequest {
    creationRequestId: string;
    partnerId: string;
  }

  /**
   * See details: https://developer.amazon.com/ja/docs/incentives-api/digital-gift-cards.html#responses-1
   */
  export interface CancelGiftCardResponse {
    creationRequestId: string;
    status: string;
  }
}
