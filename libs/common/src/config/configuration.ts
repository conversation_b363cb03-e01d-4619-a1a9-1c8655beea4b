import { ConfigFactory } from '@nestjs/config/dist/interfaces';
import moment from 'moment';
import { Configuration } from './configuration.interface';
import { IncentivesAPI } from '../providers/amazon_gift_card/amazon_gift_card.namespace';
var dotenv = require('dotenv');
var dotenvExpand = require('dotenv-expand');

var myEnv = dotenv.config();
dotenvExpand.expand(myEnv);

const int = (val: string | undefined, num: number): number =>
  val ? (isNaN(parseInt(val)) ? num : parseInt(val)) : num;
const bool = (val: string | undefined, bool: boolean): boolean =>
  val == null ? bool : val == 'true';

const configuration: Configuration = {
  frontendUrl: process.env.FRONTEND_URL ?? 'http://localhost:3000',
  emailContact: process.env.EMAIL_AUTH_FROM ?? '',
  kafka: {
    address: process.env.KAFKA_ADDRESS ?? 'http://localhost:9092',
  },
  pushNotification: {
    enable: bool(process.env.PUSH_NOTIFICATION_DEV, false),
  },
  meta: {
    appName: process.env.APP_NAME ?? 'Staart',
    appUrl: process.env.BASE_URL ?? 'http://localhost:8080',
    domainVerificationFile:
      process.env.DOMAIN_VERIFICATION_FILE ?? 'staart-verify.txt',
  },
  redis: {
    CACHE_TTL: int(process.env.REDIS_CACHE_TTL, 60),
    host: process.env.REDIS_HOST ?? 'localhost',
    port: int(process.env.REDIS_PORT, 6379),
  },
  logic: {
    maxLike: int(process.env.MAXIMUM_LIKE, 300),
    initLike: int(process.env.INIT_LIKE, 100),
    default_male_like: int(process.env.DEFAULT_MALE_LIKE, 50),
    day_add_likes: int(process.env.DAY_ADD_LIKES, 30),
    timeType_add_likes: (process.env.TIMETYPE_ADD_LIKES ??
      'day') as moment.unitOfTime.DurationConstructor,
    apple_pass_review: Boolean(process.env.APPLE_PASS_REVIEW) ?? false,
  },
  rateLimit: {
    public: {
      hourly: {
        points: int(process.env.RATE_LIMIT_PUBLIC_POINTS_HOURLY, 70000),
        duration: int(process.env.RATE_LIMIT_PUBLIC_DURATION_HOURLY, 3600),
      },
      minutely: {
        points: int(process.env.RATE_LIMIT_PUBLIC_POINTS_MINUTELY, 20000),
        duration: int(process.env.RATE_LIMIT_PUBLIC_DURATION_MINUTELY, 60),
      },
    },
    authenticated: {
      hourly: {
        points: int(process.env.RATE_LIMIT_AUTHENTICATED_POINTS_HOURLY, 70000),
        duration: int(process.env.RATE_LIMIT_AUTHENTICATED_DURATION_HOURLY, 3600),
      },
      minutely: {
        points: int(process.env.RATE_LIMIT_AUTHENTICATED_POINTS_MINUTELY, 20000),
        duration: int(process.env.RATE_LIMIT_AUTHENTICATED_DURATION_MINUTELY, 60),
      },
    },
    apiKey: {
      hourly: {
        points: int(process.env.RATE_LIMIT_API_KEY_POINTS_HOURLY, 70000),
        duration: int(process.env.RATE_LIMIT_API_KEY_DURATION_HOURLY, 3600),
      },
      minutely: {
        points: int(process.env.RATE_LIMIT_API_KEY_POINTS_MINUTELY, 20000),
        duration: int(process.env.RATE_LIMIT_API_KEY_DURATION_MINUTELY, 60),
      },
    },
  },
  caching: {
    geolocationLruSize: int(process.env.GEOLOCATION_LRU_SIZE, 100),
    apiKeyLruSize: int(process.env.API_KEY_LRU_SIZE, 100),
  },

  security: {
    saltRounds: int(process.env.SALT_ROUNDS, 10),
    jwtSecret: process.env.JWT_SECRET ?? 'staart',
    totpWindowPast: int(process.env.TOTP_WINDOW_PAST, 1),
    totpWindowFuture: int(process.env.TOTP_WINDOW_FUTURE, 0),
    mfaTokenExpiry: process.env.MFA_TOKEN_EXPIRY ?? '5m',
    mergeUsersTokenExpiry: process.env.MERGE_USERS_TOKEN_EXPIRY ?? '30m',
    accessTokenExpiry: process.env.ACCESS_TOKEN_EXPIRY ?? '1h',
    passwordPwnedCheck: bool(process.env.PASSWORD_PWNED_CHECK, true),
    unusedRefreshTokenExpiryDays: int(process.env.DELETE_EXPIRED_SESSIONS, 30),
    inactiveUserDeleteDays: int(process.env.INACTIVE_USER_DELETE_DAYS, 30),
    removedUserDeleteDays: int(process.env.REMOVED_USER_DELETE_DAYS, 30),
  },
  email: {
    name: process.env.EMAIL_NAME ?? 'Staart',
    from: process.env.EMAIL_FROM ?? '',
    retries: int(process.env.EMAIL_FAIL_RETRIES, 3),
    ses: {
      accessKeyId: process.env.EMAIL_SES_ACCESS_KEY_ID ?? '',
      secretAccessKey: process.env.EMAIL_SES_SECRET_ACCESS_KEY ?? '',
      region: process.env.EMAIL_SES_REGION ?? '',
    },
    transport: {
      host: process.env.EMAIL_HOST ?? '',
      port: int(process.env.EMAIL_PORT, 587),
      secure: bool(process.env.EMAIL_SECURE, false),
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
    },
  },
  default: {
    area: process.env.DEFAULT_AREA ?? '20km',
    lat: int(process.env.DEFAULT_LOCATION_LAT, 0),
    lon: int(process.env.DEFAULT_LOCATION_LON, 0),
  },
  webhooks: {
    retries: int(process.env.WEBHOOK_FAIL_RETRIES, 3),
  },
  sms: {
    retries: int(process.env.SMS_FAIL_RETRIES, 3),
    twilioAccountSid: process.env.TWILIO_ACCOUNT_SID ?? '',
    twilioAuthToken: process.env.TWILIO_AUTH_TOKEN ?? '',
  },

  payments: {
    apple_password: process.env.APPLE_PASSWORD ?? '',
    production: bool(process.env.PAYMENT_PRODUCTION, false),
    test: bool(process.env.PAYMENT_TEST, true),
    googleClientSecret: process.env.GOOGLE_CLIENT_SECRET ?? '',
    googleAccToken: process.env.GOOGLE_ACC_TOKEN ?? '',
    googleRefToken: process.env.GOOGLE_REF_TOKEN ?? '',
    googleClientID: process.env.GOOGLE_CLIENT_ID ?? '',
    googlePlayClientID: process.env.GOOGLEPLAY_CLIENT_ID ?? '',
    googlePlayClientSecret: process.env.GOOGLEPLAY_CLIENT_SECRET ?? '',
    googlePlayRefreshToken: process.env.GOOGLEPLAY_REFRESH_TOKEN ?? '',
  },
  tracking: {
    mode:
      (process.env.TRACKING_MODE as Configuration['tracking']['mode']) ??
      'api-key',
    index: process.env.TRACKING_INDEX ?? 'staart-logs',
    deleteOldLogs: bool(process.env.TRACKING_DELETE_OLD_LOGS, true),
    deleteOldLogsDays: int(process.env.TRACKING_DELETE_OLD_LOGS_DAYS, 90),
  },
  slack: {
    token: process.env.SLACK_TOKEN ?? '',
    slackApiUrl: process.env.SLACK_API_URL,
    rejectRateLimitedCalls: bool(
      process.env.SLACK_REJECT_RATE_LIMITED_CALLS,
      false
    ),
    retries: int(process.env.SLACK_FAIL_RETRIES, 3),
  },
  s3: {
    accessKeyId: process.env.AWS_S3_ACCESS_KEY ?? '',
    secretAccessKey: process.env.AWS_S3_SECRET_KEY ?? '',
    region: process.env.AWS_S3_REGION ?? '',
    profilePictureBucket: process.env.AWS_S3_PROFILE_PICTURE_BUCKET ?? '',
    profilePictureCdnHostname:
      process.env.AWS_S3_PROFILE_PICTURE_CDN_HOST_NAME ?? '',
    bucketName: process.env.AWS_BUCKET_NAME ?? '',
  },
  cloudinary: {
    cloudName: process.env.CLOUDINARY_CLOUD_NAME ?? '',
    apiKey: process.env.CLOUDINARY_API_KEY ?? '',
    apiSecret: process.env.CLOUDINARY_API_SECRET ?? '',
  },
  github: {
    auth: process.env.GITHUB_AUTH,
    userAgent: process.env.GITHUB_USER_AGENT,
  },
  googleMaps: {
    apiKey: process.env.GOOGLE_MAPS_API_KEY,
  },
  gravatar: {
    enabled: bool(process.env.PASSWORD_PWNED_CHECK, true),
  },
  amazonGiftCard: {
    partnerId: process.env.AMAZON_GIFT_CARD_PARTNER_ID ?? '',
    accessKeyId: process.env.AMAZON_GIFT_CARD_ACCESS_KEY ?? '',
    secretAccessKey: process.env.AMAZON_GIFT_CARD_SECRET_KEY ?? '',
    endpoint:
      process.env.AMAZON_GIFT_CARD_END_POINT == 'production'
        ? IncentivesAPI.Endpoint.JapanProduction
        : IncentivesAPI.Endpoint.JapanSandbox,
  },

  square: {
    accessToken: process.env.SQUARE_ACCESS_TOKEN ?? '',
    locationId: process.env.SQUARE_LOCATION_ID ?? '',
  },
};

const configFunction: ConfigFactory<Configuration> = () => configuration;
export default configFunction;
