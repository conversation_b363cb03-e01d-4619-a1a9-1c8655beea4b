import { Injectable, OnM<PERSON>ule<PERSON><PERSON>roy, OnModuleInit } from '@nestjs/common';
import { Expose } from './prisma.interface';
import { applyMiddleware } from './middleware';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService
  extends PrismaClient
  implements OnModuleInit, OnModuleDestroy
{
  constructor() {
    super();
  }
  async onModuleInit() {
    await this.$connect();
    applyMiddleware(this);
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }

  expose<T>(item: T): Expose<T> {
    if (!item) return {} as T;
    return item;
  }
}
