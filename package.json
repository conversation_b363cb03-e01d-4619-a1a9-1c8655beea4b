{"name": "@clout/source", "version": "0.0.0", "license": "MIT", "scripts": {"api:start": "nest start api", "api:start:dev": "nest start api --watch", "api:build": "nest build api", "x:start": "nest start x-service", "x:start:dev": "nest start x-service --watch", "x:build": "nest build x-service", "discord:start": "nest start discord-service", "discord:start:dev": "nest start discord-service --watch", "discord:build": "nest build discord-service", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main"}, "prisma": {"seed": "ts-node ./apps/api/prisma/seed_script/seed_admin.ts", "schema": "./apps/api/prisma/schema.prisma"}, "private": true, "dependencies": {"@nestjs/cache-manager": "^2.1.1", "@nestjs/common": "^10.0.2", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.2", "@nestjs/microservices": "^10.2.10", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.2", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.1.16", "@novu/node": "^0.22.0", "@prisma/client": "^5.1.1", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.3", "aws-sdk": "2.824.0", "aws4": "^1.12.0", "axios": "^1.0.0", "bcrypt": "^5.1.1", "cache-manager": "5.2.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "clone-buffer": "^1.0.0", "crypto-random-string": "3.3.0", "csv-stringify": "^6.4.5", "csv-writer": "^1.6.0", "dayjs": "^1.11.10", "dotenv": "^16.3.1", "elastic-apm-node": "^4.0.0", "geolite2-redist": "^3.0.4", "helmet": "7.0.0", "ip-range-check": "^0.2.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "maxmind": "^4.3.18", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "needle": "^3.3.1", "nest-winston": "^1.9.4", "newman": "^6.1.0", "newman-reporter-htmlextra": "^1.23.0", "oauth": "^0.10.0", "otplib": "^12.0.1", "passport": "^0.7.0", "passport-strategy": "^1.0.0", "quick-lru": "5.1.1", "rate-limiter-flexible": "^3.0.6", "reflect-metadata": "^0.1.13", "request-ip": "^3.3.0", "response-time": "^2.3.2", "rxjs": "^7.8.0", "sha256": "^0.2.0", "square": "^33.1.0", "tslib": "^2.3.0", "twilio": "^4.20.0", "twitter-api-v2": "^1.17.1", "ua-parser-js": "^1.0.37", "winston": "^3.10.0", "winston-elasticsearch": "^0.17.4"}, "devDependencies": {"@mermaid-js/mermaid-cli": "^10.6.1", "@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@nx-tools/nx-prisma": "^5.1.0", "@nx/eslint": "17.1.3", "@nx/eslint-plugin": "17.1.3", "@nx/jest": "17.1.3", "@nx/js": "17.1.3", "@nx/nest": "17.1.3", "@nx/node": "17.1.3", "@nx/webpack": "17.1.3", "@nx/workspace": "17.1.3", "@swc-node/register": "~1.6.7", "@swc/core": "~1.3.85", "@types/aws4": "^1.11.6", "@types/jest": "^29.4.0", "@types/lodash": "4.14.200", "@types/minimatch": "^5.1.2", "@types/multer": "^1.4.11", "@types/newman": "^5.3.6", "@types/node": "~18.7.1", "@types/oauth": "^0.9.4", "@types/passport-strategy": "^0.2.38", "@types/response-time": "^2.3.8", "@types/sha256": "^0.2.2", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "eslint": "~8.46.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.4.1", "jest-environment-node": "^29.4.1", "nx": "17.1.3", "prettier": "^2.6.2", "prisma": "^5.1.1", "prisma-dbml-generator": "^0.10.0", "prisma-erd-generator": "^1.11.2", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "~5.2.2"}}