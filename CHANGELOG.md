**Changelog updates:** 🔄

## 2024-06-11

### Added
- Added Docker image caching script to CI/CD pipeline.
- Introduced new stages and rules in GitLab CI configuration.

### Changed
- Updated GitLab CI configuration to include new stages and rules for changelog management.


>to commit the new content to the CHANGELOG.md file, please type:
>'/update_changelog --pr_update_changelog.push_changelog_changes=true'
- update cicd from feature/update-cicd to staging by [@khanh99](http://gitlab.lisod.vn/khanh99) in [!264](http://gitlab.lisod.vn/clout/clout-backend/-/merge_requests/264)
Preparing changelog updates...
- test ci from test-ci to staging by [@khanh99](http://gitlab.lisod.vn/khanh99) in [!266](http://gitlab.lisod.vn/clout/clout-backend/-/merge_requests/266)
null
- refactor from Fix/refactor to staging by [@huyvq](http://gitlab.lisod.vn/huyvq) in [!265](http://gitlab.lisod.vn/clout/clout-backend/-/merge_requests/265)
