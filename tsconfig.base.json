{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@clout/prisma/*": ["libs/prisma/src/lib/*"], "@clout/prisma": ["libs/prisma/src/lib"], "@clout/common/*": ["libs/common/src/*"], "@clout/common": ["libs/common/src"]}}, "exclude": ["node_modules", "tmp"]}