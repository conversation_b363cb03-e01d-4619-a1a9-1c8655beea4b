{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/api/src", "monorepo": true, "root": "apps/api", "projects": {"api": {"type": "application", "root": "apps/api", "entryFile": "main", "sourceRoot": "apps/api/src", "compilerOptions": {"tsConfigPath": "apps/api/tsconfig.app.json"}}, "common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}}}