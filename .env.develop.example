DATABASE_URL="postgresql://clout:secret@localhost:5437/clout?schema=public"
PORT = 8088
DB_NAME = clout
DB_PASSWORD=secret
DB_USER=clout
DB_PORT=5437
ENABLE_SWAGGER=true
BASE_URL="https://api.test-internal-clout.lisod.vn"
NOVU_DEVELOP_API_KEY='b49d6e4641220110d0318f54af622ac1'
NOVU_PROD_API_KEY='8ec07f2ffdec6cd7b3c5e391be027cba'
NOVU_SUBSCRIPTION_ID_DEFALT='acf8ebd4-b272-4aaa-8ccc-a7c8b13ceb2d'
PROVIDER_IDENTIFIER_NOVU_EMAIL_SUPPORT='custom-smtp-jpkbYbnan'
EMAIL_CUSTOMER='<EMAIL>'
FRONTEND_URL='https://test.clout.k.lisod.vn'

RECAPTCHA_SECRET_KEY='6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe'
AMAZON_GIFT_CARD_URL='https://agcod-v2-fe-gamma.amazon.com'
AMAZON_GIFT_CARD_PARTNER_ID='cl298'
AMAZON_GIFT_CARD_ACCESS_KEY='********************'
AMAZON_GIFT_CARD_SECRET_KEY='AT5fc7hdSBbBeso7325CK+SOU1ryvs1YLL9pWFJ4'
AMAZON_GIFT_CARD_END_POINT='sandbox'

# AMAZON_GIFT_CARD_ACCESS_KEY='********************'
# AMAZON_GIFT_CARD_SECRET_KEY='KcCeQ3mLSd8lXsb98+/Dg1ZfWo2Tb0oqMwj6Mcp0'
# AMAZON_GIFT_CARD_END_POINT='production'

TWILIO_ACCOUNT_SID='**********************************'
TWILIO_AUTH_TOKEN='e4c3a861a50f46b7633b4696d9c4ef2a'
TWILIO_PHONE_NUMBER='+***********'

X_PORT=3004
DISCORD_PORT=3005
RABBIT_MQ_URI=amqp://guest:guest@localhost:5672
RABBIT_MQ_TWITTER_QUEUE=twitter
RABBIT_MQ_DISCORD_QUEUE=discord
RABBIT_MQ_API_QUEUE=api
RABBITMQ_DEFAULT_USER=guest
RABBITMQ_DEFAULT_PASS=guest

APM_SERVER_URL=https://c1be70e580c94caca5fb93262335c9dd.apm.ap-southeast-1.aws.cloud.es.io:443
ELASTICSEARCH_URL=https://logs_collector:<EMAIL>:9243
APM_SERVER_SECRET_TOKEN= 9fgHRAHxtlB63vYcUm

SERVICE_NAME= clout-backend
ENVIRONMENT= develop

SQUARE_ACCESS_TOKEN='EAAAEC_Ttg4Oefso_I9RYoEq8MR_vTEroHgLHGXb18BEW_dP1jC7ASdQspUU6eI3'
SQUARE_LOCATION_ID='LYJN8QYK6BW5H'
# SQUARE_ENV='production'

# SQUARE_ACCESS_TOKEN='EAAAFNh_p6DuodoZmcIfPb3ofOTzaYf2qiP-jEkCizbzVNFN9k5PTZRRSe1xpWYM'
# SQUARE_LOCATION_ID='LDV2FSRZJJM88'


NEWMAN_EXPORT_PATH=./static/auto-test-report.html
NEWMAN_COLLECTION_PATH=postman/clout.postman_collection.json
NEWMAN_ENVIRONMENT_PATH=postman/clout.postman_environment.json
NEWMAN_TITLE="Clout Staging Test"
NEWMAN_WORK_DIR=postman/files

SLACK_BOT_AUTH_TOKEN=*********************************************************
SLACK_CHANNEL_ID=C06321Q4JRF
USERS_TAG="<@U03FTUG3L2H> <@U03RLTZQAPN> <@U071PAH8U12>"
NODE_ENV=STAGING

# AWS S3
AWS_BUCKET_NAME="clout-bucket-1"
AWS_S3_REGION="ap-southeast-1"
AWS_ACCESS_KEY_ID="********************"
AWS_SECRET_ACCESS_KEY="DiToEkH0Js2/3FrZ7mnEWmiGVUbRdRi8OmpOerWL"

TWITTER_TOKEN="TWs4elIwSlNUa0kyU1ZVNE56Qk9jMkZXY2xrNk1UcGphUTpyRDNEQ2ZCU1V6VTA4d3hwOHFoZEZxNlhsaDk2TkxpeDRyakk3UFJIcXRuOHlLRnRyTA=="
TWITTER_OAUTH_CLIENT_ID="Mk8zR0JSTkI2SVU4NzBOc2FWclk6MTpjaQ"
TWITTER_OAUTH_CLIENT_SECRET="rD3DCfBSUzU08wxp8qhdFq6Xlh96NLix4rjI7PRHqtn8yKFtrL"

#RateLimit
RATE_LIMIT_PUBLIC_POINTS_HOURLY=70000
RATE_LIMIT_PUBLIC_DURATION_HOURLY=3600

RATE_LIMIT_PUBLIC_POINTS_MINUTELY=20000
RATE_LIMIT_PUBLIC_DURATION_MINUTELY=60

RATE_LIMIT_AUTHENTICATED_POINTS_HOURLY=70000
RATE_LIMIT_AUTHENTICATED_DURATION_HOURLY=3600

RATE_LIMIT_AUTHENTICATED_POINTS_MINUTELY=20000
RATE_LIMIT_AUTHENTICATED_DURATION_MINUTELY=60

RATE_LIMIT_API_KEY_POINTS_HOURLY=70000
RATE_LIMIT_API_KEY_DURATION_HOURLY=3600

RATE_LIMIT_API_KEY_POINTS_MINUTELY=20000
RATE_LIMIT_API_KEY_DURATION_MINUTELY=60
