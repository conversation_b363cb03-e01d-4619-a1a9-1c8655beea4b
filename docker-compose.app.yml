version: '3'
x-shared_environment: &shared_environment
  DATABASE_URL: *********************************/clout?schema=public

services:
  api-service:
    build:
      context: .
      # target: production
    env_file:
      - .env
    environment:
      <<: *shared_environment
    volumes:
      - ./static:/usr/src/app/static
    ports:
      - ${PORT-3000}:${PORT-3000}
    networks:
      - clout_network
    restart: always

networks:
  clout_network:
    name: clout_network
    # external: true
