{"info": {"_postman_id": "c0dc3370-39f8-41cc-9d9b-fd4ebefa2ccc", "name": "[Auto test] Clout", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "25839356"}, "item": [{"name": "User", "item": [{"name": "[Success] verify-email-register (user1)", "event": [{"listen": "test", "script": {"exec": ["\r", "const requestBody = JSON.parse(pm.request.body.raw);\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", () => {\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('code');\r", "    pm.collectionVariables.set(\"userEmail_1\", requestBody?.email);\r", "    pm.collectionVariables.set(\"newUserEmail\", requestBody?.email);\r", "    pm.collectionVariables.set(\"code1\", response.code)\r", "})\r", "\r", "setTimeout(()=>{\r", "    console.log(\"Sleep 8s to wait email\")\r", "},8000)\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "isautotest", "value": "true"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{$randomEmail}}\",\r\n    \"isTest\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/verify_mail"}, "response": []}, {"name": "[Side effect] Validate Email of new User 1", "event": [{"listen": "test", "script": {"exec": ["const responseData = pm.response.json();\r", "\r", "pm.test(\"The content of the email sent to NEW User is correct\", function () {\r", "    const responseData = pm.response.json();\r", "    const items = responseData.items;\r", "    let emailUser = pm.collectionVariables.get(\"userEmail_1\");\r", "    emailUser = emailUser.toLowerCase();\r", "    const emailToUser = items.find((item) => {\r", "        const encodedString = require('btoa')(unescape(encodeURIComponent('メールアドレス')));\r", "        return item.Raw.To.includes(emailUser) && item.Content.Body.includes(encodedString)\r", "    })\r", "    if (emailToUser) {\r", "        pm.collectionVariables.set(\"UserEmailID_1\", emailToUser.ID);\r", "    }\r", "    pm.expect(emailToUser !== undefined).to.be.true;\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "basic", "basic": {"password": "{{mailhogPassword}}", "username": "{{mailhogUser}}"}}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{mailhogUrl}}/v2/search?kind=to&query={{userEmail_1}}", "host": ["{{mailhogUrl}}"], "path": ["v2", "search"], "query": [{"key": "kind", "value": "to"}, {"key": "query", "value": "{{userEmail_1}}"}]}}, "response": []}, {"name": "[Side effect] Delete Email Verify Email User 1", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Delete successful\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "basic", "basic": {"password": "{{mailhogPassword}}", "username": "{{mailhogUser}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{mailhogUrl}}/v1/messages/:mailMessageId", "host": ["{{mailhogUrl}}"], "path": ["v1", "messages", ":mailMessageId"], "variable": [{"key": "mailMessageId", "value": "{{UserEmailID_1}}"}]}}, "response": []}, {"name": "[Success] verify-email-register (user2)", "event": [{"listen": "test", "script": {"exec": ["\r", "const requestBody = JSON.parse(pm.request.body.raw);\r", "\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", () => {\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('code');\r", "    pm.collectionVariables.set(\"userEmail_2\", requestBody?.email);\r", "    pm.collectionVariables.set(\"code2\", response.code)\r", "})\r", "\r", "setTimeout(()=>{\r", "    console.log(\"Sleep 8s to wait email\")\r", "},8000)\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "isautotest", "value": "true"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{$randomEmail}}\",\r\n    \"isTest\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/verify_mail"}, "response": []}, {"name": "[Side effect] Validate Email of new User 2", "event": [{"listen": "test", "script": {"exec": ["const responseData = pm.response.json();\r", "\r", "pm.test(\"The content of the email sent to NEW User is correct\", function () {\r", "    const responseData = pm.response.json();\r", "    const items = responseData.items;\r", "    let emailUser = pm.collectionVariables.get(\"userEmail_2\");\r", "    emailUser = emailUser.toLowerCase();\r", "    const emailToUser = items.find((item) => {\r", "        const encodedString = require('btoa')(unescape(encodeURIComponent('メールアドレス')));\r", "        return item.Raw.To.includes(emailUser) && item.Content.Body.includes(encodedString)\r", "    })\r", "    if (emailToUser) {\r", "        pm.collectionVariables.set(\"UserEmailID_2\", emailToUser.ID);\r", "    }\r", "    pm.expect(emailToUser !== undefined).to.be.true;\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "basic", "basic": {"password": "{{mailhogPassword}}", "username": "{{mailhogUser}}"}}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{mailhogUrl}}/v2/search?kind=to&query={{userEmail_2}}", "host": ["{{mailhogUrl}}"], "path": ["v2", "search"], "query": [{"key": "kind", "value": "to"}, {"key": "query", "value": "{{userEmail_2}}"}]}}, "response": []}, {"name": "[Side effect] Delete Email Verify Email User 2", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Delete successful\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "basic", "basic": {"password": "{{mailhogPassword}}", "username": "{{mailhogUser}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{mailhogUrl}}/v1/messages/:mailMessageId", "host": ["{{mailhogUrl}}"], "path": ["v1", "messages", ":mailMessageId"], "variable": [{"key": "mailMessageId", "value": "{{UserEmailID_2}}"}]}}, "response": []}, {"name": "[Success] verify-email-register (user3)", "event": [{"listen": "test", "script": {"exec": ["const requestBody = JSON.parse(pm.request.body.raw);\r", "\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", () => {\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('code');\r", "    pm.collectionVariables.set(\"userEmail_3\", requestBody?.email);\r", "    pm.collectionVariables.set(\"code3\", response.code)\r", "})\r", "\r", "setTimeout(()=>{\r", "    console.log(\"Sleep 8s to wait email\")\r", "},8000)\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "isautotest", "value": "true"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{$randomEmail}}\",\r\n    \"isTest\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/verify_mail"}, "response": []}, {"name": "[Side effect] Validate Email of new user 3", "event": [{"listen": "test", "script": {"exec": ["const responseData = pm.response.json();\r", "\r", "pm.test(\"The content of the email sent to NEW User is correct\", function () {\r", "    const responseData = pm.response.json();\r", "    const items = responseData.items;\r", "    let emailUser = pm.collectionVariables.get(\"userEmail_3\");\r", "    emailUser = emailUser.toLowerCase();\r", "    const emailToUser = items.find((item) => {\r", "        const encodedString = require('btoa')(unescape(encodeURIComponent('メールアドレス')));\r", "        return item.Raw.To.includes(emailUser) && item.Content.Body.includes(encodedString)\r", "    })\r", "    if (emailToUser) {\r", "        pm.collectionVariables.set(\"UserEmailID_3\", emailToUser.ID);\r", "    }\r", "    pm.expect(emailToUser !== undefined).to.be.true;\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "basic", "basic": {"password": "{{mailhogPassword}}", "username": "{{mailhogUser}}"}}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{mailhogUrl}}/v2/search?kind=to&query={{userEmail_3}}", "host": ["{{mailhogUrl}}"], "path": ["v2", "search"], "query": [{"key": "kind", "value": "to"}, {"key": "query", "value": "{{userEmail_3}}"}]}}, "response": []}, {"name": "[Side effect] Delete Email Verify Email User 3", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Delete successful\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "basic", "basic": {"password": "{{mailhogPassword}}", "username": "{{mailhogUser}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{mailhogUrl}}/v1/messages/:mailMessageId", "host": ["{{mailhogUrl}}"], "path": ["v1", "messages", ":mailMessageId"], "variable": [{"key": "mailMessageId", "value": "{{UserEmailID_3}}"}]}}, "response": []}, {"name": "[Success] User register with email (user1)", "event": [{"listen": "test", "script": {"exec": ["const requestBody = JSON.parse(pm.request.body.raw);\r", "// Set environment\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('accessToken','refreshToken','user');\r", "    pm.environment.set(\"accessToken\", response.accessToken)\r", "    pm.environment.set(\"refreshToken\", response.refreshToken)\r", "    pm.environment.set(\"userId\", response.user.id)\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_1}}\",\r\n    \"password\": \"{{userPassword_1}}\",\r\n    \"code\": \"{{code1}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/register"}, "response": []}, {"name": "[Success] User register with email  (user2)", "event": [{"listen": "test", "script": {"exec": ["const requestBody = JSON.parse(pm.request.body.raw);\r", "// Set environment\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('accessToken','refreshToken','user');\r", "    pm.environment.set(\"accessToken2\", response.accessToken)\r", "    pm.environment.set(\"refreshToken2\", response.refreshToken)\r", "    pm.environment.set(\"userId2\", response.user.id)\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_2}}\",\r\n    \"password\": \"{{userPassword_2}}\",\r\n    \"code\": \"{{code2}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/register"}, "response": []}, {"name": "[Success] User register with email  (user3)", "event": [{"listen": "test", "script": {"exec": ["const requestBody = JSON.parse(pm.request.body.raw);\r", "// Set environment\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('accessToken','refreshToken','user');\r", "    pm.environment.set(\"accessToken3\", response.accessToken)\r", "    pm.environment.set(\"refreshToken3\", response.refreshToken)\r", "    pm.environment.set(\"userId3\", response.user.id)\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_3}}\",\r\n    \"password\": \"{{userPassword_3}}\",\r\n    \"code\": \"{{code3}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/register"}, "response": []}, {"name": "[Failed] verify-email-register with email conflict", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 400\", () => {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"This email address already exists\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_1}}\",\r\n    \"isTest\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/verify_mail"}, "response": []}, {"name": "[Failed] User register with incorrect code", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 400\", () => {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"The code is expired or incorrect\");\r", "})\r", "\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_1}}\",\r\n    \"password\": \"{{userPassword_1}}\",\r\n    \"code\": \"{{code1}}11\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/register"}, "response": []}, {"name": "[Failed] User register with email invalid by email", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 400\", () => {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"email must be an email\");\r", "})\r", "\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"invalid_emailyopmail.com\",\r\n    \"password\": \"{{userPassword_1}}\",\r\n    \"code\": \"{{code1}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/register"}, "response": []}, {"name": "[Failed] User register with email invalid by password", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 400\", () => {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"password must be longer than or equal to 8 characters\");\r", "})\r", "\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{$randomEmail}}\",\r\n    \"password\": \"1\",\r\n    \"code\": \"{{code1}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/register"}, "response": []}, {"name": "[Failed] User login with email invalid by email", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 400\", () => {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"email must be an email\");\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_1}}@@\",\r\n    \"password\": \"{{userPassword_1}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/login"}, "response": []}, {"name": "[Failed] User login with email not found by email", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"User not found\");\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"nf{{userEmail_1}}\",\r\n    \"password\": \"{{userPassword_1}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/login"}, "response": []}, {"name": "[Failed] User login with email invalid by password", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 400\", () => {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"password must be longer than or equal to 8 characters\");\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_1}}\",\r\n    \"password\": \"{{$randomAlphaNumeric}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/login"}, "response": []}, {"name": "[Failed] User login with email incorrect by password", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Incorrect password\");\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_1}}\",\r\n    \"password\": \"{{userPassword_1}}@@\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/login"}, "response": []}, {"name": "[Success] User login with email (user1)", "event": [{"listen": "test", "script": {"exec": ["const requestBody = JSON.parse(pm.request.body.raw);\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", () => {\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('accessToken', 'refreshToken', 'user');\r", "    pm.collectionVariables.set(\"accessToken_1\", response.accessToken)\r", "    pm.collectionVariables.set(\"refreshToken_1\", response.refreshToken)\r", "    pm.collectionVariables.set(\"userId_1\", response.user.id)\r", "\r", "\r", "    function generateRandomTenDigitNumber() {\r", "        const min = 1000000000; // Số nhỏ nhất có 10 chữ số\r", "        const max = 9999999999; // Số lớn nhất có 10 chữ số\r", "        return Math.floor(Math.random() * (max - min + 1) + min);\r", "    }\r", "    function generateRandomPoint() {\r", "        return Math.floor(Math.random() * 100) + 1;\r", "    }\r", "    // Set environment\r", "    pm.collectionVariables.set(\"phoneNumber\", generateRandomTenDigitNumber())\r", "    pm.collectionVariables.set(\"point1\", generateRandomPoint())\r", "    pm.collectionVariables.set(\"point2\", generateRandomPoint())\r", "    pm.collectionVariables.set(\"point3\", generateRandomPoint())\r", "    pm.collectionVariables.set(\"point4\", generateRandomPoint())\r", "    pm.collectionVariables.set(\"point5\", generateRandomPoint())\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_1}}\",\r\n    \"password\": \"{{userPassword_1}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/login"}, "response": []}, {"name": "[Success] User login with email (user2)", "event": [{"listen": "test", "script": {"exec": ["const requestBody = JSON.parse(pm.request.body.raw);\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", () => {\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('accessToken', 'refreshToken', 'user');\r", "    pm.collectionVariables.set(\"accessToken_2\", response.accessToken)\r", "    pm.collectionVariables.set(\"refreshToken_2\", response.refreshToken)\r", "    pm.collectionVariables.set(\"userId_2\", response.user.id)\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_2}}\",\r\n    \"password\": \"{{userPassword_2}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/login"}, "response": []}, {"name": "[Success] User login with email (user3)", "event": [{"listen": "test", "script": {"exec": ["const requestBody = JSON.parse(pm.request.body.raw);\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "// Test response body\r", "pm.test(\"Test data response\", () => {\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('accessToken', 'refreshToken', 'user');\r", "    pm.collectionVariables.set(\"accessToken_3\", response.accessToken)\r", "    pm.collectionVariables.set(\"refreshToken_3\", response.refreshToken)\r", "    pm.collectionVariables.set(\"userId_3\", response.user.id)\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_3}}\",\r\n    \"password\": \"{{userPassword_3}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/login"}, "response": []}, {"name": "[Success] User view profile", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "\r", "// Test for schema validation in the response\r", "pm.test(\"Schema is valid\", function () {\r", "    var schema = {\r", "        type: \"object\",\r", "        properties: {\r", "            id: { type: \"number\" },\r", "            createdAt: { type: \"string\" },\r", "            isAdmin: { type: \"boolean\" },\r", "            name: { type: \"string\" },\r", "            notificationEmail: { type: \"string\" },\r", "            prefersLanguage: { type: \"string\" },\r", "            emailId: { type: \"number\" },\r", "            profilePictureUrl: { type: \"string\" },\r", "            timezone: { type: \"string\" },\r", "            twoFactorMethod: { type: \"string\" },\r", "            twoFactorPhone: { type: [\"string\", \"null\"] },\r", "            updatedAt: { type: \"string\" },\r", "            uuid: { type: \"string\" },\r", "            deleteFlg: { type: \"boolean\" },\r", "            deletedAt: { type: [\"string\", \"null\"] },\r", "            lastActive: { type: \"string\" },\r", "            isVerified: { type: \"boolean\" },\r", "            companyId: { type: [\"number\", \"null\"] },\r", "            pointTotal: { type: \"number\" },\r", "            email: {\r", "                type: \"object\",\r", "                properties: {\r", "                    id: { type: \"number\" },\r", "                    email: { type: \"string\" },\r", "                    isVerified: { type: \"boolean\" },\r", "                    userId: { type: \"number\" },\r", "                    companyId: { type: [\"number\", \"null\"] },\r", "                    createdAt: { type: \"string\" },\r", "                    updatedAt: { type: \"string\" },\r", "                    deletedAt: { type: [\"string\", \"null\"] },\r", "                },\r", "            },\r", "            companyRole: { type: [\"string\", \"null\"] }\r", "        },\r", "\r", "    };\r", "\r", "    var response = pm.response.json();\r", "    pm.expect(tv4.validate(response, schema)).to.be.true;\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/:userId", "host": ["{{baseUrl}}"], "path": ["users", ":userId"], "variable": [{"key": "userId", "value": "{{userId_1}}"}]}}, "response": []}, {"name": "[Failed] User view profile unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}@@"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/:userId", "host": ["{{baseUrl}}"], "path": ["users", ":userId"], "variable": [{"key": "userId", "value": "{{userId_1}}"}]}}, "response": []}, {"name": "[Failed] User view profile not found userId", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"User not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/users/:userId", "host": ["{{baseUrl}}"], "path": ["users", ":userId"], "variable": [{"key": "userId", "value": "{{userId_1}}99"}]}}, "response": []}, {"name": "[Success] User request verify profile with sms", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "// Test response body\r", "pm.test(\"Test data response\", () => {\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('totpToken');\r", "    // Set environment\r", "    pm.collectionVariables.set(\"totpToken\", response?.totpToken)\r", "    pm.collectionVariables.set(\"totpCode\", response?.code)\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [{"key": "isautotest", "value": "true"}], "body": {"mode": "raw", "raw": "{\r\n    \"type\": \"SMS\",\r\n    \"isCheckPhone\": true,\r\n    \"phoneNumber\": \"{{phoneNumber}}\",\r\n    \"sendBy\": \"MESSAGE\",\r\n    \"userId\": {{userId_1}},\r\n    \"isTest\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/verification"}, "response": []}, {"name": "[Failed] User send verify profile with incorrect code", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 400\", () => {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"The code is expired or incorrect\");\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{totpToken}}\",\r\n    \"code\": \"{{totpCode}}11\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/sms"}, "response": []}, {"name": "[Failed] User send verify profile with incorrect token", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Invalid token\");\r", "})"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{totpToken}}11\",\r\n    \"code\": \"{{totpCode}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/sms"}, "response": []}, {"name": "[Success] User send verify profile with sms", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('accessToken','refreshToken','user');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{totpToken}}\",\r\n    \"code\": \"{{totpCode}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/sms"}, "response": []}, {"name": "[Success] update phone number", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"twoFactorMethod\": \"TOTP\",\r\n    \"twoFactorPhone\": \"{{phoneNumber}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me"}, "response": []}, {"name": "[Failed] update phone number unauuthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"twoFactorMethod\": \"TOTP\",\r\n    \"twoFactorPhone\": \"{{phoneNumber}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me"}, "response": []}, {"name": "[Failed] update phone number have not 10 or 11 characters", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 400\", () => {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"twoFactorPhone must be longer than or equal to 10 characters\");\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"twoFactorMethod\": \"TOTP\",\r\n    \"twoFactorPhone\": \"{{phoneNumber}}343434\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me"}, "response": []}, {"name": "[Failed] User request verify profile with phoneNumber conflict", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 409\", () => {\r", "  pm.response.to.have.status(409);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"This phone number already exists\");\r", "})"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"type\": \"SMS\",\r\n    \"phoneNumber\": \"{{phoneNumber}}\",\r\n    \"isCheckPhone\": true ,\r\n    \"sendBy\": \"MESSAGE\",\r\n    \"userId\": {{userId_1}},\r\n    \"isTest\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/verification"}, "response": []}, {"name": "[Failed] refresh token with token incorrect", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Session not found\");\r", "})"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\"token\":\"{{refreshToken_1}}111\"}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/refresh"}, "response": []}, {"name": "[Success] refresh token", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('accessToken','refreshToken');\r", "    pm.collectionVariables.set(\"accessToken_1\", response.accessToken)\r", "    pm.collectionVariables.set(\"refreshToken_1\", response.refreshToken)\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"token\": \"{{refreshToken_1}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/refresh"}, "response": []}, {"name": "[Success] change password", "event": [{"listen": "test", "script": {"exec": ["const requestBody = JSON.parse(pm.request.body.raw);\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"newPassword\": \"{{newPassword}}\",\r\n    \"password\": \"{{userPassword_1}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me"}, "response": []}, {"name": "[Failed] change password with current password incorrect", "event": [{"listen": "test", "script": {"exec": ["const requestBody = JSON.parse(pm.request.body.raw);\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Invalid credentials\");\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"newPassword\": \"{{newPassword}}\",\r\n    \"password\": \"{{userPassword_1}}@@\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me"}, "response": []}, {"name": "[Failed] Forgot password with not found email", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "    pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", () => {\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.error === 'Not Found').to.be.true\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{$randomEmail}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/forgot_password"}, "response": []}, {"name": "[Success] Forgot password", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", () => {\r", "    const response = pm.response.json();\r", "    pm.expect(response.status).to.be.true\r", "})\r", "\r", "setTimeout(()=>{\r", "    console.log(\"Sleep 8s to wait email\")\r", "},8000)\r", "\r", "\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "isautotest", "value": "true"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_1}}\",\r\n    \"isTest\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/forgot_password"}, "response": []}, {"name": "[Side effect] Validate Email of user 1 forgot password", "event": [{"listen": "test", "script": {"exec": ["const responseData = pm.response.json();\r", "\r", "pm.test(\"The content of the email sent to NEW User is correct\", function () {\r", "    const responseData = pm.response.json();\r", "    const items = responseData.items;\r", "    let emailUser = pm.collectionVariables.get(\"userEmail_1\");\r", "    emailUser = emailUser.toLowerCase();\r", "    const emailToUser = items.find((item) => {\r", "        return item.Raw.To.includes(emailUser) && item.Content.Body.includes('/auth/update-password')\r", "    })\r", "    if (emailToUser) {\r", "        pm.collectionVariables.set(\"UserEmailID_1\", emailToUser.ID);\r", "    }\r", "    pm.expect(emailToUser !== undefined).to.be.true;\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "basic", "basic": {"password": "{{mailhogPassword}}", "username": "{{mailhogUser}}"}}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{mailhogUrl}}/v2/search?kind=to&query={{userEmail_1}}", "host": ["{{mailhogUrl}}"], "path": ["v2", "search"], "query": [{"key": "kind", "value": "to"}, {"key": "query", "value": "{{userEmail_1}}"}]}}, "response": []}, {"name": "[Side effect] Delete Email Verify Email User 1", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Delete successful\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "basic", "basic": {"password": "{{mailhogPassword}}", "username": "{{mailhogUser}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{mailhogUrl}}/v1/messages/:mailMessageId", "host": ["{{mailhogUrl}}"], "path": ["v1", "messages", ":mailMessageId"], "variable": [{"key": "mailMessageId", "value": "{{UserEmailID_1}}"}]}}, "response": []}, {"name": "[Success] verify-change-email", "event": [{"listen": "test", "script": {"exec": ["\r", "const requestBody = JSON.parse(pm.request.body.raw);\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('code');\r", "    pm.collectionVariables.set(\"code1\", response.code)\r", "    pm.collectionVariables.set(\"userEmail_1\", requestBody?.email)\r", "    \r", "})\r", "\r", "setTimeout(()=>{\r", "    console.log(\"Sleep 8s to wait email\")\r", "},8000)\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "isautotest", "value": "true"}], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{$randomEmail}}\",\r\n    \"userId\": {{userId_1}},\r\n    \"isTest\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/verify_mail"}, "response": []}, {"name": "[Side effect] Validate Email of new user 1", "event": [{"listen": "test", "script": {"exec": ["const responseData = pm.response.json();\r", "\r", "pm.test(\"The content of the email sent to NEW User is correct\", function () {\r", "    const responseData = pm.response.json();\r", "    const items = responseData.items;\r", "    let emailUser = pm.collectionVariables.get(\"userEmail_1\");\r", "    emailUser = emailUser.toLowerCase();\r", "    const emailToUser = items.find((item) => {\r", "        const encodedString = require('btoa')(unescape(encodeURIComponent('メールアドレス')));\r", "        return item.Raw.To.includes(emailUser) && item.Content.Body.includes(encodedString)\r", "    })\r", "    if (emailToUser) {\r", "        pm.collectionVariables.set(\"UserEmailID_1\", emailToUser.ID);\r", "    }\r", "    pm.expect(emailToUser !== undefined).to.be.true;\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "basic", "basic": {"password": "{{mailhogPassword}}", "username": "{{mailhogUser}}"}}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{mailhogUrl}}/v2/search?kind=to&query={{userEmail_1}}", "host": ["{{mailhogUrl}}"], "path": ["v2", "search"], "query": [{"key": "kind", "value": "to"}, {"key": "query", "value": "{{userEmail_1}}"}]}}, "response": []}, {"name": "[Side effect] Delete Email Verify Email User 1", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Delete successful\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "basic", "basic": {"password": "{{mailhogPassword}}", "username": "{{mailhogUser}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{mailhogUrl}}/v1/messages/:mailMessageId", "host": ["{{mailhogUrl}}"], "path": ["v1", "messages", ":mailMessageId"], "variable": [{"key": "mailMessageId", "value": "{{UserEmailID_1}}"}]}}, "response": []}, {"name": "[Failed] change email with code incorrect", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 400\", () => {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"The code is expired or incorrect\");\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{newUserEmail}}\",\r\n    \"code\": \"{{code1}}@@\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me"}, "response": []}, {"name": "[Failed] change email with email conflict", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 400\", () => {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"The code is expired or incorrect\");\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{$randomEmail}}\",\r\n    \"code\": \"{{code1}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me"}, "response": []}, {"name": "[Success] change email", "event": [{"listen": "test", "script": {"exec": ["const requestBody = JSON.parse(pm.request.body.raw);\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_1}}\",\r\n    \"code\": \"{{code1}}\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me"}, "response": []}]}, {"name": "Company", "item": [{"name": "[Failed] User create company with unauthorized", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "isautotest", "value": ""}], "body": {"mode": "formdata", "formdata": [{"key": "companyImage", "type": "file", "src": "company_image.png", "disabled": true}, {"key": "name", "value": "{{$randomCompanyName}}", "type": "text", "disabled": true}, {"key": "code", "value": "{{$randomCompanyName}}", "type": "text", "disabled": true}, {"key": "email", "value": "{{$randomEmail}}", "type": "text", "disabled": true}, {"key": "cardInfo", "value": "ershsrstrstdj ", "type": "text", "disabled": true}, {"key": "sourceId", "value": "cnon:CBASEH3r21wGxvo1-SlZKOyBvKg", "type": "text", "disabled": true}]}, "url": "{{baseUrl}}/companies"}, "response": []}, {"name": "[Success] User create company (user1)", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "const formdata = pm.request.body.formdata\r", "const emailRequest = formdata.find((item) => item.key === 'email').value.toLowerCase()\r", "\r", "console.log('emailRequest', emailRequest)\r", "pm.collectionVariables.set(\"companyEmail_1\", emailRequest)\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 10000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(10000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", () => {\r", "    const response = pm.response.json();\r", "    pm.collectionVariables.set(\"companyId_1\", response?.company?.id)\r", "\r", "    pm.collectionVariables.set(\"companyCode_1\", response?.company?.code)\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('company');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [{"key": "isautotest", "value": "true"}], "body": {"mode": "formdata", "formdata": [{"key": "companyImage", "type": "file", "src": "company_image.png"}, {"key": "name", "value": "{{$randomCompanyName}}", "type": "text"}, {"key": "code", "value": "{{$randomCompanyName}}", "type": "text"}, {"key": "email", "value": "{{$randomEmail}}", "type": "text"}, {"key": "cardInfo", "value": "ershsrstrstdj ", "type": "text", "disabled": true}, {"key": "sourceId", "value": "cnon:CBASEH3r21wGxvo1-SlZKOyBvKg", "type": "text", "disabled": true}]}, "url": "{{baseUrl}}/companies"}, "response": []}, {"name": "[Failed] User 2 create company with email conflict", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 409\", () => {\r", "  pm.response.to.have.status(409);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"This email already exists\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "{{$randomCompanyName}}", "type": "text"}, {"key": "code", "value": "{{$randomCompanyName}}", "type": "text"}, {"key": "email", "value": "{{companyEmail_1}}", "type": "text"}, {"key": "cardInfo", "type": "file", "src": [], "disabled": true}, {"key": "sourceId", "value": "cnon:CBASEH3r21wGxvo1-SlZKOyBvKg", "type": "text", "disabled": true}, {"key": "companyImage", "type": "file", "src": "company_image.png"}]}, "url": "{{baseUrl}}/companies"}, "response": []}, {"name": "[Failed] User 2 create company with company code conflict", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 409\", () => {\r", "  pm.response.to.have.status(409);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"This code already exists\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "companyImage", "type": "file", "src": "company_image.png"}, {"key": "name", "value": "{{$randomCompanyName}}", "type": "text"}, {"key": "code", "value": "{{companyCode_1}}", "type": "text"}, {"key": "email", "value": "{{$randomEmail}}", "type": "text"}, {"key": "cardInfo", "value": "ershsrstrstdj ", "type": "text", "disabled": true}, {"key": "sourceId", "value": "cnon:CBASEH3r21wGxvo1-SlZKOyBvKg", "type": "text", "disabled": true}]}, "url": "{{baseUrl}}/companies"}, "response": []}, {"name": "[Success] User create company (user2)", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "const formdata = pm.request.body.formdata\r", "const emailRequest = formdata.find((item) => item.key === 'email').value.toLowerCase()\r", "\r", "console.log('emailRequest', emailRequest)\r", "pm.collectionVariables.set(\"companyEmail_2\", emailRequest)\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 10000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(10000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.environment.set(\"companyId_2\", response?.company?.id)\r", "    pm.environment.set(\"companyCode_2\", response?.company?.code)\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('company');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "companyImage", "type": "file", "src": "company_image.png"}, {"key": "name", "value": "{{$randomCompanyName}}", "type": "text"}, {"key": "code", "value": "{{$randomCompanyName}}", "type": "text"}, {"key": "email", "value": "{{$randomEmail}}", "type": "text"}, {"key": "cardInfo", "value": "ershsrstrstdj ", "type": "text", "disabled": true}, {"key": "sourceId", "value": "cnon:CBASEH3r21wGxvo1-SlZKOyBvKg", "type": "text", "disabled": true}]}, "url": "{{baseUrl}}/companies"}, "response": []}, {"name": "[Failed] get users in company with company id not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Company not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": "{{baseUrl}}/companies/0/users"}, "response": []}, {"name": "[Failed] get users in company with unauthorized", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}/users"}, "response": []}, {"name": "[Failed] get users in company with not permission", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 403\", () => {\r", "  pm.response.to.have.status(403);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"Insufficient permission\");\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": "{{baseUrl}}/companies/{{companyId_2}}/users"}, "response": []}, {"name": "[Success] get users in company", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('users');\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('total');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}/users"}, "response": []}, {"name": "[Success] get details company", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}"}, "response": []}, {"name": "[Failed] details company with unauthorized", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}"}, "response": []}, {"name": "[Failed] details company with company not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Company not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": "{{baseUrl}}/companies/0"}, "response": []}, {"name": "[Failed] update company with unauthorized", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "companyImage", "type": "file", "src": "image.jpg", "disabled": true}, {"key": "name", "value": "companytest129", "type": "text"}, {"key": "code", "value": "companytest129", "type": "text", "disabled": true}, {"key": "email", "value": "<EMAIL>", "type": "text"}, {"key": "cardInfo", "value": "ershsrstrstdj ", "type": "text", "disabled": true}, {"key": "sourceId", "value": "cnon:CBASEH3r21wGxvo1-SlZKOyBvKg", "type": "text", "disabled": true}]}, "url": "{{baseUrl}}/companies/{{companyId_1}}"}, "response": []}, {"name": "[Failed] update Company with company id not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Company not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "ct1214124", "type": "text"}, {"key": "code", "value": "09812", "type": "text", "disabled": true}, {"key": "email", "value": "<EMAIL>", "type": "text"}]}, "url": "{{baseUrl}}/companies/{{companyId_1}}11"}, "response": []}, {"name": "[Failed] update company with email conflict", "event": [{"listen": "test", "script": {"exec": ["const response = pm.response.json();\r", "\r", "// Test status code\r", "pm.test(\"Test status code is 409\", () => {\r", "  pm.response.to.have.status(409);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"This email already exists\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "{{$randomCompanyName}}", "type": "text"}, {"key": "email", "value": "{{companyEmail_1}}", "type": "text"}, {"key": "cardInfo", "value": "ershsrstrstdj ", "type": "text", "disabled": true}, {"key": "sourceId", "value": "cnon:CBASEH3r21wGxvo1-SlZKOyBvKg", "type": "text", "disabled": true}]}, "url": "{{baseUrl}}/companies/{{companyId_1}}"}, "response": []}, {"name": "[Success] update", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('company');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "{{$randomCompanyName}}", "type": "text"}, {"key": "code", "value": "09812", "type": "text", "disabled": true}, {"key": "email", "value": "<EMAIL>", "type": "text", "disabled": true}]}, "url": "{{baseUrl}}/companies/{{companyId_1}}"}, "response": []}, {"name": "[Failed] add user in company with unauthorized", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"123123312332{{userEmail_3}}\",\r\n    \"membership\": \"MEMBER\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/companies/{{companyId_1}}/users"}, "response": []}, {"name": "[Failed] add user in company with email not found", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"User not found\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"123123312332{{userEmail_3}}\",\r\n    \"membership\": \"MEMBER\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/companies/{{companyId_1}}/users"}, "response": []}, {"name": "[Failed] add user in company with company not found", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Company not found\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"123123312332{{userEmail_3}}\",\r\n    \"membership\": \"MEMBER\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/companies/{{companyId_1}}000/users"}, "response": []}, {"name": "[Failed] add user in company with not permission", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 403\", () => {\r", "  pm.response.to.have.status(403);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"Insufficient permission\");\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"123123312332{{userEmail_3}}\",\r\n    \"membership\": \"MEMBER\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/companies/{{companyId_2}}/users"}, "response": []}, {"name": "[Failed] add user in company with user have join company", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 409\", () => {\r", "  pm.response.to.have.status(409);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"This user has joined a company\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_2}}\",\r\n    \"membership\": \"MEMBER\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/companies/{{companyId_1}}/users"}, "response": []}, {"name": "[Success] add user in company", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 3000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(3000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Success\");\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"email\": \"{{userEmail_3}}\",\r\n    \"membership\": \"MEMBER\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/companies/{{companyId_1}}/users"}, "response": []}, {"name": "[Failed] user request join company with unauthorized", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken3}}sss"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"companyCode\": \"{{companyCode}}\",\r\n    \"isTest\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me/request/companies"}, "response": []}, {"name": "[Failed] user request join company with company not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Company not found\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_3}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"companyCode\": \"0ihiuijjopokkpkp\",\r\n    \"isTest\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me/request/companies"}, "response": []}, {"name": "[Success] user request join company", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 3000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(3000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Success\");\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_3}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"companyCode\": \"{{companyCode_1}}\",\r\n    \"isTest\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me/request/companies"}, "response": []}, {"name": "[Failed] change role user in company with unauthorized", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"membership\": \"MEMBER\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/companies/{{companyId_1}}0000/users/{{userId_3}}"}, "response": []}, {"name": "[Failed] change role user in company with user not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"User not found\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"membership\": \"MEMBER\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/companies/{{companyId_1}}/users/00000000"}, "response": []}, {"name": "[Failed] change role user in company with company not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Company not found\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"membership\": \"MEMBER\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/companies/{{companyId_1}}0000/users/{{userId_3}}"}, "response": []}, {"name": "[Failed] change role user in company with have not user role manager", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 403\", () => {\r", "  pm.response.to.have.status(403);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Need at least 1 member to be a manager in the company\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"membership\": \"MEMBER\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/companies/{{companyId_1}}/users/{{userId_1}}"}, "response": []}, {"name": "[Success] change role user in company", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('user');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"membership\": \"MEMBER\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/companies/{{companyId_1}}/users/{{userId_3}}"}, "response": []}, {"name": "[Failed] Delete the user from the company with unauthorized", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "    pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}11111/users/{{userId_3}}"}, "response": []}, {"name": "[Failed] Delete the user from the company with user not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"User not found\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}/users/9999999"}, "response": []}, {"name": "[Failed] [Failed] Delete the user from the company with company not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Company not found\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}11111/users/{{userId_3}}"}, "response": []}, {"name": "[Failed] Delete the user from the company with have not user role manager or not from company", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 403\", () => {\r", "  pm.response.to.have.status(403);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"403001: Insufficient permission\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}/users/{{userId_3}}"}, "response": []}, {"name": "[Success] Delete the user from the company", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('user');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}/users/{{userId_3}}"}, "response": []}]}, {"name": "Campaign", "item": [{"name": "[Failed] User create campaign with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "status", "value": "DRAFT", "type": "text"}, {"key": "campaignImage", "type": "file", "src": "image.jpg"}, {"key": "title", "value": "test", "type": "text"}, {"key": "category", "value": "エンターテイメント", "type": "text"}, {"key": "description", "value": "test description", "type": "text"}, {"key": "startTime", "value": "2024-02-01T08:29:42.995Z", "type": "text"}, {"key": "expiredTime", "value": "2024-03-01T08:29:42.995Z", "type": "text"}, {"key": "dontSetExpiredTime", "value": "false", "type": "text"}, {"key": "methodOfselectWinners", "value": "AUTO_PRIZEE_DRAW", "type": "text"}, {"key": "totalNumberOfUsersAllowedToWork", "value": "12", "type": "text"}, {"key": "numberOfPrizes", "value": "10", "type": "text"}, {"key": "totalPrizeValue", "value": "100", "type": "text"}, {"key": "noteReward", "value": "ádf", "type": "text"}, {"key": "settingForNotWin", "value": "false", "type": "text"}]}, "url": "{{baseUrl}}/campaigns/"}, "response": []}, {"name": "[Failed] User create campaign with don't join company", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Company not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_3}}"}}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "status", "value": "DRAFT", "type": "text"}, {"key": "campaignImage", "type": "file", "src": "image.jpg"}, {"key": "title", "value": "test", "type": "text"}, {"key": "category", "value": "エンターテイメント", "type": "text"}, {"key": "description", "value": "test description", "type": "text"}, {"key": "startTime", "value": "2024-02-01T08:29:42.995Z", "type": "text"}, {"key": "expiredTime", "value": "2024-03-01T08:29:42.995Z", "type": "text"}, {"key": "dontSetExpiredTime", "value": "false", "type": "text"}, {"key": "methodOfselectWinners", "value": "AUTO_PRIZEE_DRAW", "type": "text"}, {"key": "totalNumberOfUsersAllowedToWork", "value": "12", "type": "text"}, {"key": "numberOfPrizes", "value": "10", "type": "text"}, {"key": "totalPrizeValue", "value": "100", "type": "text"}, {"key": "noteReward", "value": "ádf", "type": "text"}, {"key": "settingForNotWin", "value": "false", "type": "text"}]}, "url": "{{baseUrl}}/campaigns/"}, "response": []}, {"name": "[Success] User create campaign", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 3000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(3000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.collectionVariables.set(\"campaignId_1\", response?.newCampaign?.id)\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('newCampaign');\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "status", "value": "DRAFT", "type": "text"}, {"key": "campaignImage", "type": "file", "src": "image.jpg"}, {"key": "title", "value": "test", "type": "text"}, {"key": "category", "value": "エンターテイメント", "type": "text"}, {"key": "description", "value": "test description", "type": "text"}, {"key": "startTime", "value": "2024-02-01T08:29:42.995Z", "type": "text"}, {"key": "expiredTime", "value": "2024-03-01T08:29:42.995Z", "type": "text", "disabled": true}, {"key": "dontSetExpiredTime", "value": "true", "type": "text"}, {"key": "methodOfselectWinners", "value": "AUTO_PRIZEE_DRAW", "type": "text"}, {"key": "totalNumberOfUsersAllowedToWork", "value": "12", "type": "text"}, {"key": "numberOfPrizes", "value": "10", "type": "text"}, {"key": "totalPrizeValue", "value": "100", "type": "text"}, {"key": "noteReward", "value": "ádf", "type": "text"}, {"key": "settingForNotWin", "value": "false", "type": "text"}]}, "url": "{{baseUrl}}/campaigns/"}, "response": []}, {"name": "[Success] User create campaign 2", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 3000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(3000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.collectionVariables.set(\"campaignId_2\", response?.newCampaign?.id)\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('newCampaign');\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "status", "value": "DRAFT", "type": "text"}, {"key": "campaignImage", "type": "file", "src": "image.jpg"}, {"key": "title", "value": "test", "type": "text"}, {"key": "category", "value": "エンターテイメント", "type": "text"}, {"key": "description", "value": "test description", "type": "text"}, {"key": "startTime", "value": "2024-02-01T08:29:42.995Z", "type": "text"}, {"key": "expiredTime", "value": "2024-03-01T08:29:42.995Z", "type": "text"}, {"key": "dontSetExpiredTime", "value": "false", "type": "text"}, {"key": "methodOfselectWinners", "value": "AUTO_PRIZEE_DRAW", "type": "text"}, {"key": "totalNumberOfUsersAllowedToWork", "value": "12", "type": "text"}, {"key": "numberOfPrizes", "value": "10", "type": "text"}, {"key": "totalPrizeValue", "value": "100", "type": "text"}, {"key": "noteReward", "value": "ádf", "type": "text"}, {"key": "settingForNotWin", "value": "false", "type": "text"}]}, "url": "{{baseUrl}}/campaigns/"}, "response": []}, {"name": "[Failed] get details campaign with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": "{{baseUrl}}/campaigns/{{campaignId_1}}"}, "response": []}, {"name": "[Failed] get details campaign with campaign not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Campaign not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": "{{baseUrl}}/campaigns/{{campaignId_1}}000000"}, "response": []}, {"name": "[Failed] get details campaign with not credentials", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Invalid credentials\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}?isAdmin=true", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}"], "query": [{"key": "isAdmin", "value": "true"}]}}, "response": []}, {"name": "[Success] details campaign (is User)", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}?token=user", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}"], "query": [{"key": "token", "value": "user"}]}}, "response": []}, {"name": "[Success] details campaign (is Admin)", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}?isAdmin=true", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}"], "query": [{"key": "isAdmin", "value": "true"}]}}, "response": []}, {"name": "[Failed] visit campaign with not unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userShareId\": \"{{userId_2}}\",\r\n  \"campaignId\": \"{{campaignId_1}}\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me/visit"}, "response": []}, {"name": "[Success] user 1 visit campaign", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"Record created successfully\", function () {\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Successfully accessed the campaign\");\r", "});\r", "\r", "pm.test(\"Each user can only create one record per campaign\", function () {\r", "    // Set up environment variable to simulate another POST request with the same userId and campaignId\r", "    const userId = pm.collectionVariables.get('userId_2');\r", "    const campaignId =  pm.collectionVariables.get('campaignId_1');\r", "    const baseUrl = pm.collectionVariables.get('baseUrl');\r", "    const token = pm.collectionVariables.get('accessToken_1');\r", "    // Simulate another request\r", "    pm.sendRequest({\r", "        url: `${baseUrl}/me/visit`,\r", "        method: 'POST',\r", "        header: {\r", "            'Content-Type': 'application/json',\r", "            'Authorization': `Bear<PERSON> ${token}`\r", "        },\r", "        body: {\r", "            mode: 'raw',\r", "            raw: JSON.stringify({ userShareId: userId, campaignId: campaignId })\r", "        }\r", "    }, function (err, res) {\r", "        pm.test(\"Subsequent request should not create another record\", function () {\r", "            pm.expect(res).to.have.status(201);\r", "            // pm.expect(res).to.be.an(\"object\").that.have.any.keys('message');\r", "            // pm.expect(res.message).to.includes(\"You have already visited this campaign\");\r", "        });\r", "    });\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"campaignId\": \"{{campaignId_1}}\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me/visit"}, "response": []}, {"name": "[Success] user 1 has already visited", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"You have already visited this campaign\", function () {\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"You have already visited this campaign\");\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"campaignId\": \"{{campaignId_1}}\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me/visit"}, "response": []}, {"name": "[Success] user 2 visit campaign", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"Record created successfully\", function () {\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Successfully accessed the campaign\");\r", "});\r", "\r", "pm.test(\"Each user can only create one record per campaign\", function () {\r", "    // Set up environment variable to simulate another POST request with the same userId and campaignId\r", "    const userId = pm.collectionVariables.get('userId_2');\r", "    const campaignId =  pm.collectionVariables.get('campaignId_1');\r", "    const baseUrl = pm.collectionVariables.get('baseUrl');\r", "    const token = pm.collectionVariables.get('accessToken_1');\r", "    // Simulate another request\r", "    pm.sendRequest({\r", "        url: `${baseUrl}/me/visit`,\r", "        method: 'POST',\r", "        header: {\r", "            'Content-Type': 'application/json',\r", "            'Authorization': `Bear<PERSON> ${token}`\r", "        },\r", "        body: {\r", "            mode: 'raw',\r", "            raw: JSON.stringify({ userShareId: userId, campaignId: campaignId })\r", "        }\r", "    }, function (err, res) {\r", "        pm.test(\"Subsequent request should not create another record\", function () {\r", "            pm.expect(res).to.have.status(201);\r", "            // pm.expect(res).to.be.an(\"object\").that.have.any.keys('message');\r", "            // pm.expect(res.message).to.includes(\"You have already visited this campaign\");\r", "        });\r", "    });\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userShareId\": \"{{userId_2}}\",\r\n  \"campaignId\": \"{{campaignId_1}}\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me/visit"}, "response": []}, {"name": "[Success] user 3 visit campaign", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"Record created successfully\", function () {\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Successfully accessed the campaign\");\r", "});\r", "\r", "pm.test(\"Each user can only create one record per campaign\", function () {\r", "    // Set up environment variable to simulate another POST request with the same userId and campaignId\r", "    const userId = pm.collectionVariables.get('userId_2');\r", "    const campaignId =  pm.collectionVariables.get('campaignId_1');\r", "    const baseUrl = pm.collectionVariables.get('baseUrl');\r", "    const token = pm.collectionVariables.get('accessToken_1');\r", "    // Simulate another request\r", "    pm.sendRequest({\r", "        url: `${baseUrl}/me/visit`,\r", "        method: 'POST',\r", "        header: {\r", "            'Content-Type': 'application/json',\r", "            'Authorization': `Bear<PERSON> ${token}`\r", "        },\r", "        body: {\r", "            mode: 'raw',\r", "            raw: JSON.stringify({ userShareId: userId, campaignId: campaignId })\r", "        }\r", "    }, function (err, res) {\r", "        pm.test(\"Subsequent request should not create another record\", function () {\r", "            pm.expect(res).to.have.status(201);\r", "            // pm.expect(res).to.be.an(\"object\").that.have.any.keys('message');\r", "            // pm.expect(res.message).to.includes(\"You have already visited this campaign\");\r", "        });\r", "    });\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_3}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"userShareId\": \"{{userId_1}}\",\r\n  \"campaignId\": \"{{campaignId_1}}\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me/visit"}, "response": []}, {"name": "[Failed] get list campaign with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": "{{baseUrl}}/campaigns/"}, "response": []}, {"name": "[Failed] get list campaign with don't join company", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Company not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_3}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/?actionFrom=ADMIN", "host": ["{{baseUrl}}"], "path": ["campaigns", ""], "query": [{"key": "actionFrom", "value": "ADMIN"}]}}, "response": []}, {"name": "[Success] list campaign (is User)", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 8000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(8000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('campaigns');\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('total');\r", "})"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"expiredTime\": {\r\n        \"sort\": \"asc\",\r\n        \"nulls\": \"last\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/campaigns?token=user", "host": ["{{baseUrl}}"], "path": ["campaigns"], "query": [{"key": "token", "value": "user"}]}}, "response": []}, {"name": "[Success] list campaign (is Admin)", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('campaigns');\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('total');\r", "})"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"expiredTime\": {\r\n        \"sort\": \"asc\",\r\n        \"nulls\": \"last\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/campaigns?actionFrom=ADMIN", "host": ["{{baseUrl}}"], "path": ["campaigns"], "query": [{"key": "actionFrom", "value": "ADMIN"}]}}, "response": []}, {"name": "[Success] check user last update campaign", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('emailUserUpdated');\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}/check", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}", "check"], "query": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTA4ODk1MDUsInNlc3Npb25JZCI6Miwic2NvcGVzIjpbXSwiaWF0IjoxNzA1MDQxMjg5LCJleHAiOjE3MDUwNDQ4ODksInN1YiI6IkxPR0lOX0FDQ0VTU19UT0tFTiJ9.GjfhUKsuur8EdvvVGuZPBNe_dzldp1DiqK_RVvSs7-M", "disabled": true}, {"key": "orderBy", "value": "", "disabled": true}]}}, "response": []}, {"name": "[Failed] update campaign with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "status", "value": "PUBLIC", "type": "text"}, {"key": "campaignImage", "type": "file", "src": [], "disabled": true}, {"key": "title", "value": "áasasá", "type": "text"}, {"key": "category", "value": "", "type": "text", "disabled": true}, {"key": "description", "value": "", "type": "text", "disabled": true}, {"key": "startTime", "value": "", "type": "text", "disabled": true}, {"key": "expiredTime", "value": "", "type": "text", "disabled": true}, {"key": "dontSetExpiredTime", "value": "", "type": "text", "disabled": true}, {"key": "methodOfselectWinners", "value": "", "type": "text", "disabled": true}, {"key": "totalNumberOfUsersAllowedToWork", "value": "", "type": "text", "disabled": true}, {"key": "numberOfPrizes", "value": "", "type": "text", "disabled": true}, {"key": "totalPrizeValue", "value": "", "type": "text", "disabled": true}, {"key": "noteReward", "value": "", "type": "text", "disabled": true}, {"key": "settingForNotWin", "value": "", "type": "text", "disabled": true}]}, "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}"], "query": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTA4ODk1MDUsInNlc3Npb25JZCI6Miwic2NvcGVzIjpbXSwiaWF0IjoxNzA1MDQxMjg5LCJleHAiOjE3MDUwNDQ4ODksInN1YiI6IkxPR0lOX0FDQ0VTU19UT0tFTiJ9.GjfhUKsuur8EdvvVGuZPBNe_dzldp1DiqK_RVvSs7-M", "disabled": true}, {"key": "orderBy", "value": "", "disabled": true}]}}, "response": []}, {"name": "[Failed] update campaign with campaign not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Campaign not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "status", "value": "PUBLIC", "type": "text"}, {"key": "campaignImage", "type": "file", "src": [], "disabled": true}, {"key": "title", "value": "áasasá", "type": "text"}, {"key": "category", "value": "", "type": "text", "disabled": true}, {"key": "description", "value": "", "type": "text", "disabled": true}, {"key": "startTime", "value": "", "type": "text", "disabled": true}, {"key": "expiredTime", "value": "", "type": "text", "disabled": true}, {"key": "dontSetExpiredTime", "value": "", "type": "text", "disabled": true}, {"key": "methodOfselectWinners", "value": "", "type": "text", "disabled": true}, {"key": "totalNumberOfUsersAllowedToWork", "value": "", "type": "text", "disabled": true}, {"key": "numberOfPrizes", "value": "", "type": "text", "disabled": true}, {"key": "totalPrizeValue", "value": "", "type": "text", "disabled": true}, {"key": "noteReward", "value": "", "type": "text", "disabled": true}, {"key": "settingForNotWin", "value": "", "type": "text", "disabled": true}]}, "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}00000", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}00000"], "query": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTA4ODk1MDUsInNlc3Npb25JZCI6Miwic2NvcGVzIjpbXSwiaWF0IjoxNzA1MDQxMjg5LCJleHAiOjE3MDUwNDQ4ODksInN1YiI6IkxPR0lOX0FDQ0VTU19UT0tFTiJ9.GjfhUKsuur8EdvvVGuZPBNe_dzldp1DiqK_RVvSs7-M", "disabled": true}, {"key": "orderBy", "value": "", "disabled": true}]}}, "response": []}, {"name": "[Failed] update campaign with not permission", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Invalid credentials\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "status", "value": "PUBLIC", "type": "text"}, {"key": "campaignImage", "type": "file", "src": [], "disabled": true}, {"key": "title", "value": "áasasá", "type": "text"}, {"key": "category", "value": "", "type": "text", "disabled": true}, {"key": "description", "value": "", "type": "text", "disabled": true}, {"key": "startTime", "value": "", "type": "text", "disabled": true}, {"key": "expiredTime", "value": "", "type": "text", "disabled": true}, {"key": "dontSetExpiredTime", "value": "", "type": "text", "disabled": true}, {"key": "methodOfselectWinners", "value": "", "type": "text", "disabled": true}, {"key": "totalNumberOfUsersAllowedToWork", "value": "", "type": "text", "disabled": true}, {"key": "numberOfPrizes", "value": "", "type": "text", "disabled": true}, {"key": "totalPrizeValue", "value": "", "type": "text", "disabled": true}, {"key": "noteReward", "value": "", "type": "text", "disabled": true}, {"key": "settingForNotWin", "value": "", "type": "text", "disabled": true}]}, "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}"], "query": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTA4ODk1MDUsInNlc3Npb25JZCI6Miwic2NvcGVzIjpbXSwiaWF0IjoxNzA1MDQxMjg5LCJleHAiOjE3MDUwNDQ4ODksInN1YiI6IkxPR0lOX0FDQ0VTU19UT0tFTiJ9.GjfhUKsuur8EdvvVGuZPBNe_dzldp1DiqK_RVvSs7-M", "disabled": true}, {"key": "orderBy", "value": "", "disabled": true}]}}, "response": []}, {"name": "[Failed] update campaign status to under_review with user role member", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Invalid credentials\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_3}}"}}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "status", "value": "UNDER_REVIEW", "type": "text"}, {"key": "title", "value": "asasss", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}"], "query": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTA4ODk1MDUsInNlc3Npb25JZCI6Miwic2NvcGVzIjpbXSwiaWF0IjoxNzA1MDQxMjg5LCJleHAiOjE3MDUwNDQ4ODksInN1YiI6IkxPR0lOX0FDQ0VTU19UT0tFTiJ9.GjfhUKsuur8EdvvVGuZPBNe_dzldp1DiqK_RVvSs7-M", "disabled": true}, {"key": "orderBy", "value": "", "disabled": true}]}}, "response": []}, {"name": "[Success] update campaign", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.environment.set(\"campaignId\", response?.newCampaign?.id)\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('newCampaign');\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "title", "value": "áasasá", "type": "text"}, {"key": "status", "value": "UNDER_REVIEW", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}"], "query": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTA4ODk1MDUsInNlc3Npb25JZCI6Miwic2NvcGVzIjpbXSwiaWF0IjoxNzA1MDQxMjg5LCJleHAiOjE3MDUwNDQ4ODksInN1YiI6IkxPR0lOX0FDQ0VTU19UT0tFTiJ9.GjfhUKsuur8EdvvVGuZPBNe_dzldp1DiqK_RVvSs7-M", "disabled": true}, {"key": "orderBy", "value": "", "disabled": true}]}}, "response": []}, {"name": "[Failed] user-complete-campaign  with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}/users", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}", "users"], "query": [{"key": "action", "value": "list", "disabled": true}]}}, "response": []}, {"name": "[Failed] user-complete-campaign with campaign not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Campaign not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}1222/users", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}1222", "users"], "query": [{"key": "action", "value": "list", "disabled": true}]}}, "response": []}, {"name": "[Failed] user-complete-campaign with not permission", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 406\", () => {\r", "  pm.response.to.have.status(406);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"You do not have permission to view this information\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}/users", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}", "users"], "query": [{"key": "action", "value": "list", "disabled": true}]}}, "response": []}, {"name": "[Success] user-complete-campaign", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('users');\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('total');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}/users", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}", "users"], "query": [{"key": "action", "value": "list", "disabled": true}]}}, "response": []}]}, {"name": "task", "item": [{"name": "[Failed] list task with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tasks/?campaignId={{campaignId_1}}", "host": ["{{baseUrl}}"], "path": ["tasks", ""], "query": [{"key": "action", "value": null, "disabled": true}, {"key": "campaignId", "value": "{{campaignId_1}}"}]}}, "response": []}, {"name": "[Failed] list task with campaign not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Campaign not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tasks/?campaignId={{campaignId_1}}344343", "host": ["{{baseUrl}}"], "path": ["tasks", ""], "query": [{"key": "action", "value": "", "disabled": true}, {"key": "campaignId", "value": "{{campaignId_1}}344343"}]}}, "response": []}, {"name": "[Success] list tasks", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('tasks');\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('total');\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tasks/?campaignId={{campaignId_1}}", "host": ["{{baseUrl}}"], "path": ["tasks", ""], "query": [{"key": "campaignId", "value": "{{campaignId_1}}"}]}}, "response": []}, {"name": "[Failed] create task with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}111\",\r\n    \"data\": [\r\n        {\r\n            \"type\": \"TWITTER\",\r\n            \"taskActionType\": \"lplpl\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"oko\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh ui\",\r\n                \"points\": 1,\r\n                \"required\": true\r\n            }\r\n        },\r\n        {\r\n            \"type\": \"TIKTOK\",\r\n            \"taskActionType\": \"okoko\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"âsas\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh u3245234345i\",\r\n                \"points\": 11,\r\n                \"required\": false\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/tasks/?campaignId={{campaignId_1}}", "host": ["{{baseUrl}}"], "path": ["tasks", ""], "query": [{"key": "action", "value": null, "disabled": true}, {"key": "campaignId", "value": "{{campaignId_1}}"}]}}, "response": []}, {"name": "[Failed] create task with campaignId not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Campaign not found\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}1111\",\r\n    \"data\": [\r\n        {\r\n            \"type\": \"TWITTER\",\r\n            \"taskActionType\": \"lplpl\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"oko\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh ui\",\r\n                \"points\": 1,\r\n                \"required\": true\r\n            }\r\n        },\r\n        {\r\n            \"type\": \"TIKTOK\",\r\n            \"taskActionType\": \"okoko\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"âsas\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh u3245234345i\",\r\n                \"points\": 11,\r\n                \"required\": false\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Failed] create task with company not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Company not found\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_3}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"data\": [\r\n        {\r\n            \"type\": \"TWITTER\",\r\n            \"taskActionType\": \"lplpl\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"oko\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh ui\",\r\n                \"points\": 1,\r\n                \"required\": true\r\n            }\r\n        },\r\n        {\r\n            \"type\": \"TIKTOK\",\r\n            \"taskActionType\": \"okoko\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"âsas\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh u3245234345i\",\r\n                \"points\": 11,\r\n                \"required\": false\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Success] create task", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.collectionVariables.set(\"taskId1\", response?.[0]?.id)\r", "    pm.collectionVariables.set(\"taskId2\", response?.[1]?.id)\r", "    pm.collectionVariables.set(\"taskId3\", response?.[2]?.id)\r", "    pm.collectionVariables.set(\"taskId4\", response?.[3]?.id)\r", "    pm.expect(response).to.be.an(\"array\").that.have.length.gte(1);\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"data\": [\r\n        {\r\n            \"type\": \"TWITTER\",\r\n            \"taskActionType\": \"lplpl\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"oko\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh ui\",\r\n                \"points\": {{point1}},\r\n                \"required\": false\r\n            }\r\n        },\r\n        {\r\n            \"type\": \"TIKTOK\",\r\n            \"taskActionType\": \"hehe\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"âsas\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh u3245234345i\",\r\n                \"points\": {{point2}},\r\n                \"required\": false\r\n            }\r\n        },\r\n        {\r\n            \"type\": \"TIKTOK\",\r\n            \"taskActionType\": \"haha\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"ssss\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh u3245234345i\",\r\n                \"points\": {{point3}},\r\n                \"required\": true\r\n            }\r\n        },\r\n        {\r\n            \"type\": \"TIKTOK\",\r\n            \"taskActionType\": \"huhu\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"111111\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh u3245234345i\",\r\n                \"points\": {{point4}},\r\n                \"required\": true\r\n            }\r\n        },\r\n        {\r\n            \"type\": \"SHARE_URL\",\r\n            \"taskActionType\": \"SHARE_URL\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"SHARE_URL\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh u3245234345i\",\r\n                \"points\": {{point5}},\r\n                \"required\": false\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Failed] update task with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"data\": [\r\n        {\r\n            \"taskId\": {{taskId1}},\r\n            \"type\": \"TWITTER\",\r\n            \"taskActionType\": \"ko999992222\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"oko\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh ui\",\r\n                 \"points\": 11,\r\n                \"required\": true\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Failed] update task with company not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 403\", () => {\r", "  pm.response.to.have.status(403);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"403001: Insufficient permission\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_3}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"data\": [\r\n        {\r\n            \"taskId\": {{taskId1}},\r\n            \"type\": \"TWITTER\",\r\n            \"taskActionType\": \"ko999992222\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"oko\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh ui\",\r\n                \"points\": 11,\r\n                \"required\": true\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks"}, "response": []}, {"name": "[Failed] update task with campaign not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Campaign not found\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId}}121221211212\",\r\n    \"data\": [\r\n        {\r\n            \"taskId\": {{taskId1}},\r\n            \"type\": \"TWITTER\",\r\n            \"taskActionType\": \"ko999992222\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"oko\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh ui\",\r\n                \"points\": 11,\r\n                \"required\": true\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Failed] update task with campaign does not belong to the company", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 403\", () => {\r", "  pm.response.to.have.status(403);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Insufficient permission\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken2}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId}}\",\r\n    \"data\": [\r\n        {\r\n            \"taskId\": {{taskId1}},\r\n            \"type\": \"TWITTER\",\r\n            \"taskActionType\": \"ko999992222\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"oko\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh ui\",\r\n                \"points\": 11,\r\n                \"required\": true\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Failed] update task with task not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 400\", () => {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Task not found\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId}}\",\r\n    \"data\": [\r\n        {\r\n            \"taskId\": 99999999,\r\n            \"type\": \"TWITTER\",\r\n            \"taskActionType\": \"ko999992222\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"oko\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh ui\",\r\n                \"points\": 11,\r\n                \"required\": true\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Success] update task", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"array\").that.have.length.gte(1);\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"data\": [\r\n        {\r\n            \"taskId\": {{taskId1}},\r\n            \"type\": \"TWITTER\",\r\n            \"taskActionType\": \"ko999992222\",\r\n            \"taskTemplate\": {\r\n                \"userName\": \"oko\",\r\n                \"link\": \"https://youtube.com\",\r\n                \"config\": \"efiosuh ui\",\r\n                 \"points\": {{point1}},\r\n                \"required\": false\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Failed] user complete task with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userShareId\": {{userId_2}},\r\n    \"answer\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/me/tasks/{{taskId4}}", "host": ["{{baseUrl}}"], "path": ["me", "tasks", "{{taskId4}}"], "query": [{"key": "action", "value": null, "disabled": true}]}}, "response": []}, {"name": "[Failed] user complete task with user share not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"User not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userShareId\": 9999999,\r\n    \"answer\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/me/tasks/{{taskId4}}", "host": ["{{baseUrl}}"], "path": ["me", "tasks", "{{taskId4}}"], "query": [{"key": "action", "value": null, "disabled": true}]}}, "response": []}, {"name": "[Failed] user complete task with task not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"404004: Task not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userShareId\": {{userId_2}},\r\n    \"answer\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/me/tasks/{{taskId4}}9999", "host": ["{{baseUrl}}"], "path": ["me", "tasks", "{{taskId4}}9999"], "query": [{"key": "action", "value": null, "disabled": true}]}}, "response": []}, {"name": "[Success] user share complete task 4", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 3000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(3000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"answer\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/me/tasks/{{taskId4}}"}, "response": []}, {"name": "[Success] user share complete task 3", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 3000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(3000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"answer\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/me/tasks/{{taskId3}}", "host": ["{{baseUrl}}"], "path": ["me", "tasks", "{{taskId3}}"], "query": [{"key": "action", "value": null, "disabled": true}]}}, "response": []}, {"name": "[Success] user 2 complete task 4", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 3000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(3000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userShareId\": {{userId_1}},\r\n    \"answer\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/me/tasks/{{taskId4}}", "host": ["{{baseUrl}}"], "path": ["me", "tasks", "{{taskId4}}"], "query": [{"key": "action", "value": null, "disabled": true}]}}, "response": []}, {"name": "[Success] user 2 complete task 3", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 3000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(3000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userShareId\": {{userId_1}},\r\n    \"answer\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/me/tasks/{{taskId3}}", "host": ["{{baseUrl}}"], "path": ["me", "tasks", "{{taskId3}}"], "query": [{"key": "action", "value": null, "disabled": true}]}}, "response": []}, {"name": "[Success] user 2 complete task 2", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 3000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(3000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userShareId\": {{userId_1}},\r\n    \"answer\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/me/tasks/{{taskId2}}", "host": ["{{baseUrl}}"], "path": ["me", "tasks", "{{taskId2}}"], "query": [{"key": "action", "value": null, "disabled": true}]}}, "response": []}, {"name": "[Success] user 2 complete task 1", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 3000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(3000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"userShareId\": {{userId_1}},\r\n    \"answer\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/me/tasks/{{taskId1}}", "host": ["{{baseUrl}}"], "path": ["me", "tasks", "{{taskId1}}"], "query": [{"key": "action", "value": null, "disabled": true}]}}, "response": []}]}, {"name": "Campaign-chart", "item": [{"name": "[Failed] get user complete all task required in campaign with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}/users?action=list", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}", "users"], "query": [{"key": "action", "value": "list"}]}}, "response": []}, {"name": "[Failed] get user complete task in campaign action not in list,csv,chart", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 501\", () => {\r", "  pm.response.to.have.status(501);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Action not implemented\");\r", "    \r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}/users?action=test", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}", "users"], "query": [{"key": "action", "value": "test"}]}}, "response": []}, {"name": "[Success] list user complete task and check point in campaign", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('users');\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('total');\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('amountAwarded');\r", "    pm.expect(response.total).to.equal(2);\r", "})\r", "\r", "// Test specific user points\r", "pm.test(\"Check specific user points\", () => {\r", "  const response = pm.response.json();\r", "  const users = response.users;\r", "  const userId1 = pm.collectionVariables.get(\"userId_1\")\r", "  const userId2 = pm.collectionVariables.get(\"userId_2\")\r", "  const point1 = parseFloat(pm.collectionVariables.get(\"point1\"));\r", "  const point2 = parseFloat(pm.collectionVariables.get(\"point2\"));\r", "  const point3 = parseFloat(pm.collectionVariables.get(\"point3\"));\r", "  const point4 = parseFloat(pm.collectionVariables.get(\"point4\"));\r", "  const point5 = parseFloat(pm.collectionVariables.get(\"point5\"));\r", "\r", "\r", "  // Find the user with userId_1 and check their score\r", "  const user1 = users.find(user => user.userId == userId1);\r", "  pm.expect(user1).to.not.be.undefined;\r", "  const pointUser1 = parseFloat(user1.points);\r", "  pm.expect(pointUser1).to.equal(point3 + point4 + point5);\r", "\r", "  // Find the user with userId_2 and check their score\r", "  const user2 = users.find(user => user.userId == userId2);\r", "  pm.expect(user2).to.not.be.undefined;\r", "  const pointUser2 = parseFloat(user2.points);\r", "  pm.expect(pointUser2).to.equal(point1 + point2 + point3 + point4);\r", "});\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}/users?action=list", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}", "users"], "query": [{"key": "action", "value": "list"}]}}, "response": []}, {"name": "[Failed] get statistics campaign with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}/statistics?action=list", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}", "statistics"], "query": [{"key": "action", "value": "list"}]}}, "response": []}, {"name": "[Failed] get statistics campaign action not in list,csv,chart", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 501\", () => {\r", "  pm.response.to.have.status(501);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Action not implemented\");\r", "    \r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}/statistics?action=test", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}", "statistics"], "query": [{"key": "action", "value": "test"}]}}, "response": []}, {"name": "[Success] list statistics campaign", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('data');\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('total');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}/statistics?action=list", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}", "statistics"], "query": [{"key": "action", "value": "list"}]}}, "response": []}, {"name": "[Success] chart statistics campaign", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response time\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body structure\r", "pm.test(\"Test data response structure\", () => {\r", "  const response = pm.response.json();\r", "  pm.expect(response).to.be.an(\"object\").that.has.property('data');\r", "  \r", "  const data = response.data;\r", "  pm.expect(data).to.be.an(\"object\");\r", "  \r", "  // Get today's date in YYYY-MM-DD format\r", "  const today = new Date().toISOString().split('T')[0];\r", "  pm.expect(data).to.have.property(today);\r", "  \r", "  const todayData = data[today];\r", "  pm.expect(todayData).to.be.an(\"object\").that.has.all.keys(\r", "    'joinCampaignToday',\r", "    'joinCampaignTotal',\r", "    'visitCampaignToday',\r", "    'visitCampaignTotal'\r", "  );\r", "});\r", "\r", "// Test value logic\r", "pm.test(\"Test value logic\", () => {\r", "  const response = pm.response.json();\r", "  const data = response.data;\r", "  \r", "  // Get today's date in YYYY-MM-DD format\r", "  const today = new Date().toISOString().split('T')[0];\r", "  \r", "  const todayData = data[today];\r", "  \r", "  pm.expect(todayData.joinCampaignToday).to.be.a('number');\r", "  pm.expect(todayData.joinCampaignTotal).to.be.a('number');\r", "  pm.expect(todayData.visitCampaignToday).to.be.a('number');\r", "  pm.expect(todayData.visitCampaignTotal).to.be.a('number');\r", "  \r", "  // Check if visitCampaignToday is not less than joinCampaignToday\r", "  pm.expect(todayData.visitCampaignToday).to.be.at.least(todayData.joinCampaignToday);\r", "  \r", "  // Check if visitCampaignTotal is not less than joinCampaignTotal\r", "  pm.expect(todayData.visitCampaignTotal).to.be.at.least(todayData.joinCampaignTotal);\r", "  \r", "  pm.test(\"Test value\", () => {\r", "    pm.expect(todayData.joinCampaignToday).to.equal(2);\r", "    pm.expect(todayData.joinCampaignTotal).to.equal(2);\r", "    pm.expect(todayData.visitCampaignToday).to.equal(3);\r", "    pm.expect(todayData.visitCampaignTotal).to.equal(3);\r", "  });\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/campaigns/{{campaignId_1}}/statistics?action=chart", "host": ["{{baseUrl}}"], "path": ["campaigns", "{{campaignId_1}}", "statistics"], "query": [{"key": "action", "value": "chart"}]}}, "response": []}]}, {"name": "reward", "item": [{"name": "[Failed] list reward with campaign not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Campaign not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/rewards/?campaignId={{campaignId_1}}11212", "host": ["{{baseUrl}}"], "path": ["rewards", ""], "query": [{"key": "action", "value": null, "disabled": true}, {"key": "campaignId", "value": "{{campaignId_1}}11212"}]}}, "response": []}, {"name": "[Success] list reward", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('rewards');\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('total');\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/rewards/?campaignId={{campaignId_1}}", "host": ["{{baseUrl}}"], "path": ["rewards", ""], "query": [{"key": "campaignId", "value": "{{campaignId_1}}"}]}}, "response": []}, {"name": "[Success] create reward with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}11\",\r\n    \"data\": [\r\n        {\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 1,\r\n            \"amountOfMoney\": 12,\r\n            \"numberOfWinningTicket\": 123\r\n        },\r\n        {\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 2,\r\n            \"amountOfMoney\": 122,\r\n            \"numberOfWinningTicket\": 1223\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}, {"name": "[Failed] create reward with company not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Company not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_3}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"data\": [\r\n        {\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 1,\r\n            \"amountOfMoney\": 12,\r\n            \"numberOfWinningTicket\": 123\r\n        },\r\n        {\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 2,\r\n            \"amountOfMoney\": 122,\r\n            \"numberOfWinningTicket\": 1223\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}, {"name": "[Failed] create reward with campaignId not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Campaign not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}11\",\r\n    \"data\": [\r\n        {\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 1,\r\n            \"amountOfMoney\": 12,\r\n            \"numberOfWinningTicket\": 123\r\n        },\r\n        {\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 2,\r\n            \"amountOfMoney\": 122,\r\n            \"numberOfWinningTicket\": 1223\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}, {"name": "[Success] create reward", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 201\", () => {\r", "  pm.response.to.have.status(201);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.environment.set(\"rewardId\", response?.[0]?.id)\r", "    pm.environment.set(\"rewardId2\", response?.[1]?.id)\r", "    pm.expect(response).to.be.an(\"array\").that.have.length.gte(1);\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"data\": [\r\n        {\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 1,\r\n            \"amountOfMoney\": 12,\r\n            \"numberOfWinningTicket\": 123\r\n        },\r\n        {\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 2,\r\n            \"amountOfMoney\": 122,\r\n            \"numberOfWinningTicket\": 1223\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}, {"name": "[Failed] update reward with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"data\": [\r\n        {\r\n            \"rewardId\": {{rewardId}},\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 1,\r\n            \"amountOfMoney\": 1243,\r\n            \"numberOfWinningTicket\": 123\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}, {"name": "[Failed] update reward with campaign not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Campaign not found\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}*********\",\r\n    \"data\": [\r\n        {\r\n            \"rewardId\": {{rewardId}},\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 1,\r\n            \"amountOfMoney\": 1243,\r\n            \"numberOfWinningTicket\": 123\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}, {"name": "[Failed] update reward with campaign does not belong to the company", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 403\", () => {\r", "  pm.response.to.have.status(403);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Insufficient permission\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_3}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"data\": [\r\n        {\r\n            \"rewardId\": {{rewardId}},\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 1,\r\n            \"amountOfMoney\": 1243,\r\n            \"numberOfWinningTicket\": 123\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}, {"name": "[Failed] update reward with reward not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 400\", () => {\r", "  pm.response.to.have.status(400);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"<PERSON><PERSON> not found\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"data\": [\r\n        {\r\n            \"rewardId\": 999999,\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 1,\r\n            \"amountOfMoney\": 1243,\r\n            \"numberOfWinningTicket\": 123\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}, {"name": "[Success] update reward", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 2000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(2000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"array\").that.have.length.gte(1);\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"data\": [\r\n        {\r\n            \"rewardId\": {{rewardId}},\r\n            \"type\": \"AMAZON_GIFT\",\r\n            \"index\": 1,\r\n            \"amountOfMoney\": 1243,\r\n            \"numberOfWinningTicket\": 123\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}]}, {"name": "MaintenancePeriod", "item": [{"name": "[Success] <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["const requestBody = JSON.parse(pm.request.body.raw);", "", "// Test status code", "pm.test(\"Test status code is 200\", () => {", "    pm.response.to.have.status(200);", "});", "", "// Test response times", "pm.test(\"Test response time is less than 1000ms\", () => {", "    pm.expect(pm.response.responseTime).to.be.below(1000);", "});", "", "// Test response body", "pm.test(\"Test data response\", () => {", "    const response = pm.response.json();", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('accessToken', 'refreshToken', 'user');", "    pm.expect(response.user.isAdmin === true).to.be.true;", "    pm.collectionVariables.set(\"accessTokenAdmin\", response.accessToken)", "    pm.collectionVariables.set(\"refreshTokenAdmin\", response.refreshToken)", "    pm.collectionVariables.set(\"adminId\", response.user.id)", "})", "", "", ""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"email\": \"{{adminEmail}}\",\n    \"password\": \"{{adminPassword}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/auth/login"}, "response": []}, {"name": "[Failed] Create new maintenance missing time", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Schema is valid\", function () {", "    var schema = {", "        type: \"object\",", "        properties: {", "            statusCode: { type: \"number\" },", "            message: { type: \"array\" },", "            error: { type: \"string\" },", "        }", "    };", "", "    var response = pm.response.json();", "    console.log('response.message', response.message)", "    pm.expect(tv4.validate(response, schema)).to.be.true;", "", "    pm.expect(response.message.includes('endAt must be a Date instance')).to.be.true;", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"startAt\": \"2024-05-30T05:08:01.992Z\",\n    \"description\": \"AUTO TEST CREATE MAINTENANCE MISSING endAt\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/maintenance-periods"}, "response": []}, {"name": "[Failed] Create new maintenance with an account other than the admin role", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 403\", function () {", "    pm.response.to.have.status(403);", "});", "", "// Test response body", "pm.test(\"Test data response\", ()=>{", "    const response = pm.response.json();", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');", "    pm.expect(response.message).to.includes(\"Forbidden\");", "", "})", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"startAt\": \"2050-05-01T05:08:01.992Z\",\n    \"endAt\": \"2050-05-21T05:13:06.599Z\",\n    \"description\": \"AUTO TEST CREATE MAINTENANCE\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/maintenance-periods"}, "response": []}, {"name": "[Success] Create new maintenance", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test(\"Schema is valid\", function () {", "    var schema = {", "        type: \"object\",", "        properties: {", "            id: { type: \"number\" },", "            startAt: { type: \"string\" },", "            endAt: { type: \"string\" },", "            description: { type: \"string\" },", "            status: { type: \"string\" },", "            updatedAt: { type: \"string\" },", "            deleteAt: { type: [\"null\", \"string\"] },", "            createdAt: { type: \"string\" }", "        }", "    };", "", "    var response = pm.response.json();", "    pm.expect(tv4.validate(response, schema)).to.be.true;", "    pm.collectionVariables.set('maintenanceID', response.id)", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"startAt\": \"2050-05-01T05:08:01.992Z\",\n    \"endAt\": \"2050-05-21T05:13:06.599Z\",\n    \"description\": \"AUTO TEST CREATE MAINTENANCE\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/maintenance-periods"}, "response": []}, {"name": "[Failed] Create new maintenance coincided with another maintenance period", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 409\", function () {", "    pm.response.to.have.status(409);", "});", "", "pm.test(\"Schema is valid\", function () {", "    var schema = {", "        type: \"object\",", "        properties: {", "            message: { type: \"string\" },", "            error: { type: \"string\" },", "            statusCode: { type: \"number\" },", "        }", "    };", "", "    var response = pm.response.json();", "    pm.expect(tv4.validate(response, schema)).to.be.true;", "    pm.expect(response.error === \"Conflict\").to.be.true;", "});", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"startAt\": \"2050-05-02T05:08:01.992Z\",\n    \"endAt\": \"2050-05-20T05:13:06.599Z\",\n    \"description\": \"AUTO TEST CREATE MAINTENANCE\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/maintenance-periods"}, "response": []}, {"name": "[Success] Get list maintenance", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// Test to validate the response schema", "pm.test(\"Schema is valid\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      data: {", "        type: \"object\",", "        properties: {", "          data: {", "            type: \"array\"", "          },", "          total: {", "            type: \"number\"", "          }", "        },", "        required: [\"data\", \"total\"]", "      },", "      status: {", "        type: \"string\"", "      }", "    },", "    required: [\"data\", \"status\"]", "  };", "", "  var response = pm.response.json();", "  pm.expect(tv4.validate(response, schema)).to.be.true;", "});"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/maintenance-periods"}, "response": []}, {"name": "[Success] Get Detail maintenance", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Schema is valid\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      id: { type: \"number\" },", "      startAt: { type: \"string\" },", "      endAt: { type: \"string\" },", "      description: { type: \"string\" },", "      status: { type: \"string\" },", "      updatedAt: { type: \"string\" },", "      deleteAt: { type: [\"null\", \"string\"] },", "      createdAt: { type: \"string\" }", "    }", "  };", "", "  var response = pm.response.json();", "  pm.expect(tv4.validate(response, schema)).to.be.true;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/maintenance-periods/:maintenanceId", "host": ["{{baseUrl}}"], "path": ["maintenance-periods", ":maintenanceId"], "variable": [{"key": "maintenanceId", "value": "{{maintenanceID}}"}]}}, "response": []}, {"name": "[Success] Update Detail maintenance", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Schema is valid\", function () {", "  var schema = {", "    type: \"object\",", "    properties: {", "      id: { type: \"number\" },", "      startAt: { type: \"string\" },", "      endAt: { type: \"string\" },", "      description: { type: \"string\" },", "      status: { type: \"string\" },", "      updatedAt: { type: \"string\" },", "      deleteAt: { type: [\"null\", \"string\"] },", "      createdAt: { type: \"string\" }", "    }", "  };", "", "  var response = pm.response.json();", "  pm.expect(tv4.validate(response, schema)).to.be.true;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"startAt\": \"2024-05-30T05:08:01.992Z\",\n    \"endAt\": \"2024-05-30T05:10:01.992Z\",\n    \"description\": \"メンテナンス理由\\n\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/maintenance-periods/{{maintenanceID}}"}, "response": []}, {"name": "[Failed] Create new maintenance with an account other than the admin role", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 403\", function () {", "    pm.response.to.have.status(403);", "});", "", "// Test response body", "pm.test(\"Test data response\", ()=>{", "    const response = pm.response.json();", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');", "    pm.expect(response.message).to.includes(\"Forbidden\");", "", "})", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_2}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"startAt\": \"2024-05-30T05:08:01.992Z\",\n    \"endAt\": \"2024-05-30T05:10:01.992Z\",\n    \"description\": \"メンテナンス理由\\n\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/maintenance-periods/{{maintenanceID}}"}, "response": []}, {"name": "[Failed] Update Detail maintenance not found id", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test(\"Schema is valid\", function () {", "    var schema = {", "        type: \"object\",", "        properties: {", "            statusCode: { type: \"number\" },", "            message: { type: \"string\" },", "            error: { type: \"string\" },", "        }", "    };", "", "    var response = pm.response.json();", "    pm.expect(tv4.validate(response, schema)).to.be.true;", "    pm.expect(response.message === 'Maintenance period not found').to.be.true;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"startAt\": \"2024-05-30T05:08:01.992Z\",\n    \"endAt\": \"2024-05-30T05:10:01.992Z\",\n    \"description\": \"メンテナンス理由\\n\"\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/maintenance-periods/{{maintenanceID}}111"}, "response": []}]}, {"name": "Delete data", "item": [{"name": "[Success] delete reward1", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Success\");\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"rewardIds\": [{{rewardId}}]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}, {"name": "[Failed] remove user in company with unauthorized", "event": [{"listen": "test", "script": {"exec": ["// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message')\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}/users/{{userId_3}}"}, "response": []}, {"name": "[Failed] remove user in company with company not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Company not found\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}000/users/{{userId_3}}"}, "response": []}, {"name": "[Failed] remove user in company with user not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"User not found\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}/users/00000000"}, "response": []}, {"name": "[Failed] change role user in company with have not user role manager <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 403\", () => {\r", "  pm.response.to.have.status(403);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Need at least 1 member to be a manager in the company\");\r", "\r", "})\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}/users/{{userId_1}}"}, "response": []}, {"name": "[Failed] delete reward with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId}}\",\r\n    \"rewardIds\": [{{rewardId2}}]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Failed] delete reward with campaign not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Campaign not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId}}3443343434\",\r\n    \"rewardIds\": [{{rewardId2}}]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}, {"name": "[Failed] delete reward with campaign does not belong to the company", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 403\", () => {\r", "  pm.response.to.have.status(403);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Insufficient permission\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_3}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"rewardIds\": [{{rewardId2}}]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}, {"name": "[Success] delete reward2", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Success\");\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"rewardIds\": [{{rewardId2}}]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/rewards/"}, "response": []}, {"name": "[Failed] delete task with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"taskIds\": [{{taskId2}}]\r\n    }", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Failed] delete task with campaign not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Campaign not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}*********\",\r\n    \"taskIds\": [{{taskId2}}]\r\n    }", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Failed] delete task with campaign does not belong to the company", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 403\", () => {\r", "  pm.response.to.have.status(403);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Insufficient permission\");\r", "})\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_3}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"taskIds\": [{{taskId2}}]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Success] delete task 1", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Success\");\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"taskIds\": [\"{{taskId1}}\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks"}, "response": []}, {"name": "[Success] delete task 2", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Success\");\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"campaignId\": \"{{campaignId_1}}\",\r\n    \"taskIds\": [\"{{taskId2}}\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/tasks/"}, "response": []}, {"name": "[Failed] delete campaign with unauthorized", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 401\", () => {\r", "  pm.response.to.have.status(401);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Unauthorized\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/campaigns/{{campaignId_2}}"}, "response": []}, {"name": "[Failed] delete campaign with campaign not found", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 404\", () => {\r", "  pm.response.to.have.status(404);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Campaign not found\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/campaigns/{{campaignId_2}}000"}, "response": []}, {"name": "[Failed] delete campaign with campaign status is not draft", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 403\", () => {\r", "  pm.response.to.have.status(403);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');\r", "    pm.expect(response.message).to.includes(\"Cannot delete campaigns\");\r", "\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/campaigns/{{campaignId_1}}"}, "response": []}, {"name": "[Success] delete campaign 2", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/campaigns/{{campaignId_2}}"}, "response": []}, {"name": "delete campaign1", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessToken_1}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"isTest\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": "{{baseUrl}}/campaigns/{{campaignId_1}}"}, "response": []}, {"name": "delete company1", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 5000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(5000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/companies/{{companyId_1}}"}, "response": []}, {"name": "delete company2", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/companies/{{companyId_2}}"}, "response": []}, {"name": "delete user1", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/users/{{userId_1}}"}, "response": []}, {"name": "delete user 2", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/users/{{userId_2}}"}, "response": []}, {"name": "delete user 3", "event": [{"listen": "test", "script": {"exec": ["\r", "// Test status code\r", "pm.test(\"Test status code is 200\", () => {\r", "  pm.response.to.have.status(200);\r", "});\r", "\r", "// Test response times\r", "pm.test(\"Test response time is less than 1000ms\", () => {\r", "  pm.expect(pm.response.responseTime).to.be.below(1000);\r", "});\r", "\r", "// Test response body\r", "pm.test(\"Test data response\", ()=>{\r", "    const response = pm.response.json();\r", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('id');\r", "})"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "DELETE", "header": [], "url": "{{baseUrl}}/users/{{userId_3}}"}, "response": []}, {"name": "[Failed] Delete Detail maintenance with unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test(\"Schema is valid\", function () {", "    var schema = {", "        type: \"object\",", "        properties: {", "            statusCode: { type: \"number\" },", "            message: { type: \"string\" },", "            error: { type: \"string\" },", "        }", "    };", "", "    var response = pm.response.json();", "    pm.expect(response).to.be.an(\"object\").that.have.any.keys('message');", "    pm.expect(response.message).to.includes(\"Unauthorized\");", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": {{maintenanceID}},\n    \"startAt\": \"2024-05-30T05:08:01.992Z\",\n    \"endAt\": \"2024-05-30T05:10:01.992Z\",\n    \"description\": \"メンテナンス理由\\n\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/maintenance-periods/:maintenanceId", "host": ["{{baseUrl}}"], "path": ["maintenance-periods", ":maintenanceId"], "variable": [{"key": "maintenanceId", "value": "{{maintenanceID}}"}]}}, "response": []}, {"name": "[Failed] Delete Detail maintenance notfound id", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test(\"Schema is valid\", function () {", "    var schema = {", "        type: \"object\",", "        properties: {", "            statusCode: { type: \"number\" },", "            message: { type: \"string\" },", "            error: { type: \"string\" },", "        }", "    };", "", "    var response = pm.response.json();", "    pm.expect(tv4.validate(response, schema)).to.be.true;", "    pm.expect(response.message === 'Maintenance period not found').to.be.true;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "DELETE", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": {{maintenanceID}},\n    \"startAt\": \"2024-05-30T05:08:01.992Z\",\n    \"endAt\": \"2024-05-30T05:10:01.992Z\",\n    \"description\": \"メンテナンス理由\\n\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/maintenance-periods/:maintenanceId", "host": ["{{baseUrl}}"], "path": ["maintenance-periods", ":maintenanceId"], "variable": [{"key": "maintenanceId", "value": "{{maintenanceID}}9999"}]}}, "response": []}, {"name": "[Success] Delete maintenance", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// Add a test to check for schema validation in the response", "pm.test(\"Schema validation for the response is successful\", function () {", "    var schema = {", "        type: \"object\",", "        properties: {", "            id: { type: \"number\" },", "            startAt: { type: \"string\" },", "            endAt: { type: \"string\" },", "            description: { type: \"string\" },", "            status: { type: \"string\" },", "            updatedAt: { type: \"string\" },", "            deleteAt: { type: \"string\" },", "            createdAt: { type: \"string\" }", "        },", "", "    };", "", "    var response = pm.response.json();", "    pm.expect(tv4.validate(response, schema)).to.be.true;", "    pm.expect(response.deleteAt !== null).to.be.true;", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": {"token": "{{accessTokenAdmin}}"}}, "method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/maintenance-periods/:maintenanceId", "host": ["{{baseUrl}}"], "path": ["maintenance-periods", ":maintenanceId"], "variable": [{"key": "maintenanceId", "value": "{{maintenanceID}}"}]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "totpToken", "value": "", "type": "string"}, {"key": "totpCode", "value": "", "type": "string"}, {"key": "campaignId", "value": "", "type": "string"}, {"key": "companyId", "value": "", "type": "string"}, {"key": "companyCode", "value": "", "type": "string"}, {"key": "taskId", "value": "", "type": "string"}, {"key": "rewardId", "value": "", "type": "string"}, {"key": "campaignId2", "value": "", "type": "string"}, {"key": "userId2", "value": "", "type": "string"}, {"key": "baseUrl", "value": "https://api.staging.clout-fi.com/v1", "type": "string"}, {"key": "mailhogUser", "value": "lisod", "type": "string"}, {"key": "mailhogPassword", "value": "LisodVietnam2023@", "type": "string"}, {"key": "mailhogProject", "value": "clout", "type": "string"}, {"key": "mailhogUrl", "value": "https://mailhog.lisod.vn/api", "type": "string"}, {"key": "userEmail_1", "value": "", "type": "string"}, {"key": "password", "value": "", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}, {"key": "refreshToken", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "userEmail_2", "value": ""}, {"key": "userEmail_3", "value": ""}, {"key": "userPassword_1", "value": "pwTest@1", "type": "string"}, {"key": "userPassword_2", "value": "pwTest@2", "type": "string"}, {"key": "userPassword_3", "value": "pwTest@3", "type": "string"}, {"key": "code1", "value": ""}, {"key": "accessToken_1", "value": ""}, {"key": "refreshToken_1", "value": ""}, {"key": "userId_1", "value": ""}, {"key": "phoneNumber", "value": ""}, {"key": "newPassword", "value": "newPwTest@321", "type": "string"}, {"key": "adminEmail", "value": "<EMAIL>", "type": "string"}, {"key": "adminPassword", "value": "ApxvelYT4ENwHmm4", "type": "string"}, {"key": "accessTokenAdmin", "value": ""}, {"key": "refreshTokenAdmin", "value": ""}, {"key": "adminId", "value": ""}, {"key": "maintenanceID", "value": ""}, {"key": "newUserEmail", "value": "", "type": "string"}, {"key": "code2", "value": ""}, {"key": "code3", "value": ""}, {"key": "UserEmailID_1", "value": ""}, {"key": "UserEmailID_2", "value": ""}, {"key": "UserEmailID_3", "value": ""}, {"key": "accessToken_2", "value": ""}, {"key": "refreshToken_2", "value": ""}, {"key": "userId_2", "value": ""}, {"key": "companyEmail_1", "value": ""}, {"key": "companyId_1", "value": ""}, {"key": "companyCode_1", "value": ""}, {"key": "accessToken_3", "value": ""}, {"key": "refreshToken_3", "value": ""}, {"key": "userId_3", "value": ""}, {"key": "companyEmail_2", "value": ""}, {"key": "campaignId_1", "value": ""}, {"key": "campaignId_2", "value": ""}, {"key": "taskId2", "value": ""}, {"key": "taskId1", "value": ""}, {"key": "taskId3", "value": ""}, {"key": "taskId4", "value": ""}, {"key": "point1", "value": "", "type": "string"}, {"key": "point2", "value": "", "type": "string"}, {"key": "point3", "value": "", "type": "string"}, {"key": "point4", "value": "", "type": "string"}, {"key": "point5", "value": "", "type": "string"}]}