{"id": "6358ff0f-55f6-478b-9b17-a9e75288b6da", "name": "Clout local", "values": [{"key": "baseUrl", "value": "https://api.staging.clout-fi.com/v1", "type": "default", "enabled": true}, {"key": "mailhogUser", "value": "lisod", "type": "default", "enabled": true}, {"key": "mailhogPassword", "value": "LisodVietnam2023@", "type": "default", "enabled": true}, {"key": "mailhogProject", "value": "clout", "type": "default", "enabled": true}, {"key": "mailhogUrl", "value": "https://mailhog.lisod.vn/api", "type": "default", "enabled": true}, {"key": "email", "value": "", "type": "any", "enabled": true}, {"key": "password", "value": "", "type": "any", "enabled": true}, {"key": "accessToken", "value": "", "type": "any", "enabled": true}, {"key": "refreshToken", "value": "", "type": "any", "enabled": true}, {"key": "userId", "value": "", "type": "default", "enabled": true}, {"key": "email2", "value": "", "type": "default", "enabled": true}, {"key": "password2", "value": "", "type": "default", "enabled": true}, {"key": "accessToken2", "value": "", "type": "default", "enabled": true}, {"key": "userId2", "value": "", "type": "default", "enabled": true}, {"key": "email3", "value": "", "type": "default", "enabled": true}, {"key": "password3", "value": "", "type": "default", "enabled": true}, {"key": "accessToken3", "value": "", "type": "default", "enabled": true}, {"key": "userId3", "value": "", "type": "default", "enabled": true}, {"key": "totpToken", "value": "", "type": "default", "enabled": true}, {"key": "totpCode", "value": "", "type": "default", "enabled": true}, {"key": "campaignId", "value": "", "type": "default", "enabled": true}, {"key": "companyId", "value": "", "type": "default", "enabled": true}, {"key": "companyCode", "value": "", "type": "default", "enabled": true}, {"key": "companyId2", "value": "", "type": "default", "enabled": true}, {"key": "companyCode2", "value": "", "type": "default", "enabled": true}, {"key": "taskId", "value": "", "type": "default", "enabled": true}, {"key": "taskId2", "value": "", "type": "default", "enabled": true}, {"key": "rewardId", "value": "", "type": "default", "enabled": true}, {"key": "rewardId2", "value": "", "type": "default", "enabled": true}, {"key": "campaignId2", "value": "", "type": "default", "enabled": true}, {"key": "code1", "value": "", "type": "default", "enabled": true}, {"key": "code2", "value": "", "type": "default", "enabled": true}, {"key": "code3", "value": "", "type": "default", "enabled": true}, {"key": "phoneNumber", "value": "", "type": "default", "enabled": true}, {"key": "refreshToken2", "value": "", "type": "any", "enabled": true}, {"key": "refreshToken3", "value": "", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-05-22T09:46:45.225Z", "_postman_exported_using": "Postman/11.1.3"}